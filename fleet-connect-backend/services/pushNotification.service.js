import admin from "firebase-admin";
import db from "../models/index.js";

const { Driver, Mechanic } = db;

// Initialize Firebase Admin SDK
let firebaseApp;

const initializeFirebase = () => {
  if (!firebaseApp) {
    try {
      console.log("🔧 Initializing Firebase Admin SDK...");

      // Check if already initialized
      if (admin.apps.length > 0) {
        firebaseApp = admin.apps[0];
        console.log("✅ Using existing Firebase app");
        return;
      }

      // Validate required environment variables
      if (!process.env.FIREBASE_PROJECT_ID || !process.env.FIREBASE_PRIVATE_KEY || !process.env.FIREBASE_CLIENT_EMAIL) {
        throw new Error("Missing required Firebase environment variables");
      }

      const serviceAccount = {
        type: "service_account",
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n"),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        client_id: process.env.FIREBASE_CLIENT_ID,
        auth_uri: "https://accounts.google.com/o/oauth2/auth",
        token_uri: "https://oauth2.googleapis.com/token",
        auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
        client_x509_cert_url: process.env.FIREBASE_CLIENT_CERT_URL,
      };

      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID,
      });

      console.log("✅ Firebase Admin SDK initialized successfully");
      console.log("   - Project ID:", process.env.FIREBASE_PROJECT_ID);

    } catch (error) {
      console.error("❌ Failed to initialize Firebase:", error.message);
      firebaseApp = null;
    }
  }
};

// Initialize Firebase on module load
initializeFirebase();

// Notification deduplication cache
const notificationCache = new Map();
const CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes

const pushNotificationService = {
  // Generate unique notification key for deduplication
  generateNotificationKey: (userId, type, serviceRequestId) => {
    return `${userId}_${type}_${serviceRequestId}`;
  },

  // Check if notification was recently sent
  isNotificationRecent: (key) => {
    const cached = notificationCache.get(key);
    if (!cached) return false;

    const now = Date.now();
    if (now - cached.timestamp > CACHE_EXPIRY) {
      notificationCache.delete(key);
      return false;
    }

    return true;
  },

  // Mark notification as sent
  markNotificationSent: (key) => {
    notificationCache.set(key, { timestamp: Date.now() });
  },

  // Send notification to a specific user
  sendToUser: async (userId, userType, notification) => {
    try {
      // Initialize Firebase if not already done
      if (!firebaseApp) {
        initializeFirebase();
      }

      if (!firebaseApp) {
        console.warn("Firebase not initialized, skipping notification");
        return;
      }

      // Get user's FCM token from database
      let user;
      if (userType === "driver") {
        user = await Driver.findByPk(userId);
      } else if (userType === "mechanic") {
        user = await Mechanic.findByPk(userId);
      }

      if (!user || !user.fcm_token) {
        console.warn(`No FCM token found for ${userType} ${userId}`);
        return;
      }

      // Check if token is expired
      if (
        user.fcm_token_expires_at &&
        new Date() > new Date(user.fcm_token_expires_at)
      ) {
        console.warn(`FCM token expired for ${userType} ${userId}`);
        return;
      }

      // Convert all data values to strings (FCM requirement)
      const stringifiedData = {};
      if (notification.data) {
        Object.keys(notification.data).forEach(key => {
          stringifiedData[key] = String(notification.data[key]);
        });
      }

      const message = {
        token: user.fcm_token,
        notification: {
          title: notification.title,
          body: notification.body,
        },
        data: stringifiedData,
        android: {
          notification: {
            sound: "default",
            priority: "high",
            channelId: "default",
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: notification.title,
                body: notification.body,
              },
              sound: "default",
              badge: 1,
              "content-available": 1,
            },
          },
        },
      };

      const response = await admin.messaging().send(message);
      console.log("✅ Push notification sent successfully:", response);
      return response;
    } catch (error) {
      console.error("❌ Error sending push notification:", error.code, error.message);

      // Handle specific FCM errors
      if (error.code === 'messaging/invalid-registration-token') {
        console.log("💡 FCM token is invalid - user needs to login again");
      } else if (error.code === 'messaging/third-party-auth-error') {
        console.log("💡 APNS/FCM auth error - check Firebase project configuration");
      }

      // Don't throw error to prevent breaking the flow
      return null;
    }
  },

  // Send notification to multiple users
  sendToMultipleUsers: async (userTokens, notification) => {
    try {
      // Initialize Firebase if not already done
      if (!firebaseApp) {
        initializeFirebase();
      }

      console.log("Sending notification to tokens:", userTokens);
      if (!firebaseApp || !userTokens.length) {
        console.warn("Firebase not initialized or no tokens provided");
        return;
      }

      // Convert all data values to strings (FCM requirement)
      const stringifiedData = {};
      if (notification.data) {
        Object.keys(notification.data).forEach(key => {
          stringifiedData[key] = String(notification.data[key]);
        });
      }

      const message = {
        tokens: userTokens,
        notification: {
          title: notification.title,
          body: notification.body,
        },
        data: stringifiedData,
        android: {
          notification: {
            sound: "default",
            priority: "high",
            channelId: "default",
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: notification.title,
                body: notification.body,
              },
              sound: "default",
              badge: 1,
              "content-available": 1,
            },
          },
        },
      };

      // Send individual messages to each token (most compatible approach)
      const promises = userTokens.map((token, index) => {
        const individualMessage = {
          token: token,
          notification: message.notification,
          data: message.data,
          android: message.android,
          apns: message.apns,
        };

        console.log(`📤 Sending FCM message ${index + 1}/${userTokens.length}:`, {
          token: token.substring(0, 20) + '...',
          title: individualMessage.notification.title,
          body: individualMessage.notification.body
        });

        return admin.messaging().send(individualMessage)
          .then(response => {
            console.log(`✅ FCM message ${index + 1} sent successfully:`, response);
            return response;
          })
          .catch(error => {
            console.error(`❌ FCM message ${index + 1} failed:`, error.code, error.message);
            throw error;
          });
      });

      const responses = await Promise.allSettled(promises);
      const successCount = responses.filter(
        (r) => r.status === "fulfilled"
      ).length;
      const failureCount = userTokens.length - successCount;

      console.log(
        `✅ Push notifications sent: ${successCount}/${userTokens.length} successful`
      );

      // Log individual failures for debugging
      if (failureCount > 0) {
        responses.forEach((response, idx) => {
          if (response.status === "rejected") {
            console.error(`❌ Failed to send to token ${idx}:`, response.reason?.code, response.reason?.message);
          }
        });
      }

      return { successCount, failureCount };
    } catch (error) {
      console.error("❌ Error sending push notifications:", error.code, error.message);

      // Return failed response instead of throwing
      return {
        successCount: 0,
        failureCount: userTokens.length,
        error: error.message
      };
    }
  },

  // Notification templates with tone information
  notifications: {
    newServiceRequest: (driverName, issueType) => ({
      title: "🚛 New Service Request",
      body: `${driverName} needs help with ${issueType}`,
      data: {
        type: "new_service_request",
        action: "view_requests",
        tone: "urgent",
      },
    }),

    mechanicAssigned: (mechanicName) => ({
      title: "🔧 Mechanic Assigned",
      body: `${mechanicName} has been assigned to your request`,
      data: {
        type: "mechanic_assigned",
        action: "view_request_details",
        tone: "urgent",
      },
    }),

    mechanicAccepted: (mechanicName) => ({
      title: "✅ Request Accepted",
      body: `${mechanicName} accepted your request and is on the way`,
      data: {
        type: "mechanic_accepted",
        action: "view_chat",
        tone: "success",
      },
    }),

    mechanicEnRoute: (mechanicName, eta) => ({
      title: "🚗 Mechanic En Route",
      body: `${mechanicName} is on the way. ETA: ${eta} minutes`,
      data: {
        type: "mechanic_en_route",
        action: "view_location",
        tone: "urgent",
      },
    }),

    serviceCompleted: (mechanicName) => ({
      title: "✅ Service Completed",
      body: mechanicName
        ? `${mechanicName} has completed your service request`
        : "Your service request has been completed",
      data: {
        type: "service_completed",
        action: "rate_service",
        tone: "success",
      },
    }),

    newChatMessage: (senderName, messagePreview) => ({
      title: `💬 Message from ${senderName}`,
      body: messagePreview,
      data: {
        type: "new_chat_message",
        action: "open_chat",
        tone: "informative",
      },
    }),

    emergencyAlert: (title, message) => ({
      title: `🚨 ${title}`,
      body: message,
      data: {
        type: "emergency",
        action: "immediate_attention",
        tone: "emergency",
      },
    }),

    requestCancelled: () => ({
      title: "Request Cancelled",
      body: "Your service request has been cancelled",
      data: {
        type: "request_cancelled",
        action: "view_requests",
      },
    }),
  },

  // Send specific notification types
  sendNewServiceRequestNotification: async (
    mechanicIds,
    driverName,
    issueType,
    issueDescription,
    vehicleDetails,
    serviceRequestId
  ) => {
    try {
      // Get FCM tokens for all mechanics
      const mechanics = await Mechanic.findAll({
        where: { id: mechanicIds },
        attributes: ["fcm_token", "fcm_token_expires_at"],
      });

      // Filter out expired tokens
      const validTokens = mechanics
        .filter((m) => {
          if (!m.fcm_token) return false;
          if (
            m.fcm_token_expires_at &&
            new Date() > new Date(m.fcm_token_expires_at)
          ) {
            console.warn(`FCM token expired for mechanic ${m.id}`);
            return false;
          }
          return true;
        })
        .map((m) => m.fcm_token);

      const tokens = validTokens;

      if (tokens.length === 0) {
        console.warn("No FCM tokens found for mechanics");
        return;
      }

      const notification = {
        ...pushNotificationService.notifications.newServiceRequest(
          driverName,
          issueType
        ),
        data: {
          ...pushNotificationService.notifications.newServiceRequest(
            driverName,
            issueType,
            issueDescription,
            vehicleDetails
          ).data,
          service_request_id: serviceRequestId,
        },
      };

      console.log("Sending notification to mechanics:", tokens, notification);
      return await pushNotificationService.sendToMultipleUsers(
        tokens,
        notification
      );
    } catch (error) {
      console.error("Error sending new service request notification:", error);
    }
  },

  // Notify all active mechanics about a new service request
  notifyAllMechanicsNewRequest: async (
    driverName,
    issueType,
    issueDescription,
    vehicleDetails,
    serviceRequestId,
    location,
    additionalData = {}
  ) => {
    try {
      console.log("🔔 Sending notifications to all active mechanics...");
      console.log("🔍 FCM Backend Debug - issueDescription:", issueDescription);
      console.log("🔍 FCM Backend Debug - vehicleDetails:", vehicleDetails);

      // Check if this notification was recently sent
      const notificationKey = pushNotificationService.generateNotificationKey(
        'all_mechanics',
        'new_service_request',
        serviceRequestId
      );

      if (pushNotificationService.isNotificationRecent(notificationKey)) {
        console.log("🔄 Skipping duplicate notification for service request:", serviceRequestId);
        return;
      }

      // Get all active mechanics with FCM tokens
      const mechanics = await Mechanic.findAll({
        where: {
          is_active: true,
          fcm_token: { [db.Sequelize.Op.ne]: null },
        },
        attributes: ["id", "name", "fcm_token", "fcm_token_expires_at"],
      });

      // Filter out expired tokens
      const validMechanics = mechanics.filter((m) => {
        if (
          m.fcm_token_expires_at &&
          new Date() > new Date(m.fcm_token_expires_at)
        ) {
          console.warn(`FCM token expired for mechanic ${m.id}`);
          return false;
        }
        return true;
      });

      if (validMechanics.length === 0) {
        console.warn("⚠️ No active mechanics with FCM tokens found");
        return;
      }

      console.log(
        `📱 Found ${validMechanics.length} active mechanics to notify`
      );

      const tokens = validMechanics.map((m) => m.fcm_token);

      const notification = {
        ...pushNotificationService.notifications.newServiceRequest(
          driverName,
          issueType
        ),
        data: {
          ...pushNotificationService.notifications.newServiceRequest(
            driverName,
            issueType,
            issueDescription,
            vehicleDetails
          ).data,
          service_request_id: serviceRequestId,
          driver_name: driverName,
          issue_type: issueType,
          issue_description: issueDescription || "Service request",
          truck_info: vehicleDetails || "",
          driver_phone: additionalData.driver_phone || "",
          latitude: location?.latitude?.toString() || "",
          longitude: location?.longitude?.toString() || "",
        },
      };

      const result = await pushNotificationService.sendToMultipleUsers(
        tokens,
        notification
      );

      // Mark notification as sent to prevent duplicates
      pushNotificationService.markNotificationSent(notificationKey);

      console.log(
        `✅ Notifications sent to ${result?.successCount || 0}/${
          mechanics.length
        } mechanics`
      );

      return result;
    } catch (error) {
      console.error(
        "❌ Error notifying mechanics about new service request:",
        error
      );
    }
  },

  sendChatMessageNotification: async (
    recipientId,
    recipientType,
    senderName,
    messagePreview,
    serviceRequestId
  ) => {
    try {
      const notification = {
        ...pushNotificationService.notifications.newChatMessage(
          senderName,
          messagePreview
        ),
        data: {
          ...pushNotificationService.notifications.newChatMessage(
            senderName,
            messagePreview
          ).data,
          service_request_id: serviceRequestId,
        },
      };

      return await pushNotificationService.sendToUser(
        recipientId,
        recipientType,
        notification
      );
    } catch (error) {
      console.error("Error sending chat message notification:", error);
    }
  },
};

export default pushNotificationService;
