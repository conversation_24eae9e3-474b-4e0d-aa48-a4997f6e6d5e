// server.js
// Import environment config FIRST to ensure dotenv.config() runs before any other imports
import "./config/env.js";

import express from "express";
import cors from "cors";
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import jwt from "jsonwebtoken";
import db from "./models/index.js";

// Route imports
import driverRoutes from "./routes/driver.routes.js";
import serviceRequestRoutes from "./routes/serviceRequest.routes.js";
import mechanicRoutes from "./routes/mechanic.routes.js";
import transportCompanyRoutes from "./routes/transportCompany.routes.js";
import truckRepairCompanyRoutes from "./routes/truckRepairCompany.routes.js";
import chatRoutes from "./routes/chat.routes.js";
import fileRoutes from "./routes/file.routes.js";
import locationRoutes from "./routes/location.routes.js";
import serviceAssignmentRoutes from "./routes/serviceAssignment.routes.js";
import partnershipRoutes from "./routes/partnership.routes.js";
import adminRoutes from "./routes/admin.routes.js";
import navigationRoutes from "./routes/navigation.routes.js";


const PORT = process.env.PORT || 5001;
const HOST = process.env.HOST || "0.0.0.0"; // Bind to all interfaces for mobile device access
const JWT_SECRET = process.env.JWT_SECRET || "your_jwt_secret";
const NODE_ENV = process.env.NODE_ENV || "development";

// Production-specific configurations
const isProduction = NODE_ENV === "production";

const app = express();
const server = createServer(app);
const allowedOrigins = isProduction
  ? [
      "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com",
      "http://localhost:5173",
      "http://localhost:5174"
    ]
  : [
      "http://localhost:5173",
      "http://localhost:5174",
      "http://localhost:3000",
      "http://************:5001"
    ];

const io = new SocketIOServer(server, {
  cors: {
    origin: allowedOrigins,
    methods: ["GET", "POST"],
    credentials: true,
  },
  // Mobile-friendly configuration
  pingTimeout: 60000, // 60 seconds before considering connection dead
  pingInterval: 25000, // Send ping every 25 seconds
  transports: ["polling", "websocket"], // Start with polling for stability
  allowEIO3: true, // Allow Engine.IO v3 clients
  connectTimeout: 45000, // Connection timeout
  upgradeTimeout: 10000, // Upgrade timeout
});

// === Middlewares ===
app.use(express.json());
app.use(
  cors({
    origin: allowedOrigins,
    credentials: true,
  })
);

// Health check endpoint for mobile device testing
app.get("/api/health", (req, res) => {
  res.json({
    status: "OK",
    message: "Fleet Connect Backend is running",
    timestamp: new Date().toISOString(),
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });
});

// Serve static files (uploaded images)
app.use("/uploads", express.static("uploads"));

// Make io available to controllers
app.set("io", io);

// Also store app globally for internal calls
global.app = app;

// Health check endpoint for network testing
app.get("/health", (req, res) => {
  console.log("🏥 Health check requested from:", req.ip);
  res.status(200).json({
    status: "OK",
    message: "Server is running",
    timestamp: new Date().toISOString(),
    platform: "backend",
  });
});

// === Mount Routes ===
app.use("/api/drivers", driverRoutes);
app.use("/api/requests", serviceRequestRoutes);
app.use("/api/mechanics", mechanicRoutes);
app.use("/api/transport-companies", transportCompanyRoutes);
app.use("/api/truck-repair-companies", truckRepairCompanyRoutes);
app.use("/api/chat", chatRoutes);
app.use("/api/files", fileRoutes);
app.use("/api/location", locationRoutes);
app.use("/api/assignments", serviceAssignmentRoutes);
app.use("/api/partnerships", partnershipRoutes);
app.use("/api/admin", adminRoutes);
app.use("/api/navigation", navigationRoutes);

// === WebSocket Authentication & Events ===
io.use((socket, next) => {
  try {
    console.log("🔐 Socket.IO auth attempt:", {
      hasAuth: !!socket.handshake.auth,
      authKeys: Object.keys(socket.handshake.auth || {}),
      hasToken: !!socket.handshake.auth?.token,
    });

    const token = socket.handshake.auth.token;
    if (!token) {
      console.error("❌ No token provided in Socket.IO auth");
      return next(new Error("Authentication error"));
    }

    console.log("🔑 Verifying token...");
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log(
      "✅ Token verified for user:",
      decoded.id,
      "role:",
      decoded.role
    );

    socket.userId = decoded.id;
    socket.userRole = decoded.role;
    next();
  } catch (err) {
    console.error("❌ Socket.IO auth error:", err.message);
    next(new Error("Authentication error"));
  }
});

io.on("connection", (socket) => {
  console.log(`User ${socket.userId} (${socket.userRole}) connected`);

  // Join driver room for receiving mechanic assignments
  socket.on("join_driver_room", ({ driverId }) => {
    socket.join(`driver_${driverId}`);
    console.log(
      `Driver ${socket.userId} joined driver room: driver_${driverId}`
    );
  });

  // Join mechanic room for receiving service requests
  socket.on("join_mechanic_room", ({ mechanicId }) => {
    socket.join(`mechanic_${mechanicId}`);
    console.log(
      `Mechanic ${socket.userId} joined mechanic room: mechanic_${mechanicId}`
    );
  });

  // Join service request room
  socket.on("join_service_request", (serviceRequestId) => {
    socket.join(`service_request_${serviceRequestId}`);
    console.log(
      `User ${socket.userId} joined service request ${serviceRequestId}`
    );
  });

  // Leave service request room
  socket.on("leave_service_request", (serviceRequestId) => {
    socket.leave(`service_request_${serviceRequestId}`);
    console.log(
      `User ${socket.userId} left service request ${serviceRequestId}`
    );
  });

  // Handle typing indicators
  socket.on("typing", ({ serviceRequestId, isTyping }) => {
    socket.to(`service_request_${serviceRequestId}`).emit("user_typing", {
      userId: socket.userId,
      userRole: socket.userRole,
      isTyping,
    });
  });

  // Handle real-time location sharing
  socket.on(
    "share_location",
    ({ serviceRequestId, latitude, longitude, heading, speed }) => {
      socket.to(`service_request_${serviceRequestId}`).emit("location_update", {
        user_id: socket.userId,
        user_type: socket.userRole,
        latitude,
        longitude,
        heading,
        speed,
        timestamp: new Date(),
        service_request_id: serviceRequestId,
      });
    }
  );

  // Handle mechanic status updates
  socket.on(
    "status_update",
    ({ serviceRequestId, status, message, is_active, timestamp }) => {
      if (serviceRequestId) {
        // Mechanic status update for specific service
        socket
          .to(`service_request_${serviceRequestId}`)
          .emit("mechanic_status_update", {
            mechanic_id: socket.userId,
            status,
            message,
            timestamp: new Date(),
          });
      } else if (socket.userRole === "driver" && is_active !== undefined) {
        // Driver status update (active/inactive)
        console.log(
          `📱 Driver ${socket.userId} status update: ${
            is_active ? "active" : "inactive"
          }`
        );
        // Could broadcast to relevant mechanics or store in database
      }
    }
  );

  // Handle location updates from drivers
  socket.on(
    "location_update",
    ({ latitude, longitude, accuracy, speed, heading, timestamp }) => {
      if (socket.userRole === "driver") {
        console.log(
          `📍 Driver ${socket.userId} location update: ${latitude}, ${longitude}`
        );
        // Could broadcast to connected mechanics or store in database
        // For now, just log it
      }
    }
  );

  // Handle chat messages
  socket.on(
    "send_message",
    ({ serviceRequestId, message, messageType = "text", timestamp }) => {
      console.log(
        `💬 Message from ${socket.userRole} ${socket.userId} in service ${serviceRequestId}`
      );

      // Broadcast message to all users in the service request room
      socket.to(`service_request_${serviceRequestId}`).emit("new_message", {
        serviceId: serviceRequestId,
        serviceRequestId,
        message,
        senderType: socket.userRole,
        senderId: socket.userId,
        senderName: socket.userName || `${socket.userRole}_${socket.userId}`,
        timestamp: timestamp || new Date().toISOString(),
        messageType,
      });
    }
  );

  // Handle ping from clients for connection health monitoring
  socket.on("ping", (data) => {
    // Respond with pong to confirm connection is alive
    socket.emit("pong", {
      timestamp: Date.now(),
      clientTimestamp: data?.timestamp
    });
  });

  socket.on("disconnect", (reason) => {
    console.log(`User ${socket.userId} disconnected: ${reason}`);
  });
});

// === Health Check ===
app.get("/", (_req, res) => res.send("✅ FleetConnect API is running"));

// === Sync & Start ===
console.log("🔄 Syncing database...");
db.sequelize
  .sync({ alter: true }) // alter:true updates tables without dropping data
  .then(() => {
    console.log("✅ Database synced successfully");
    // Starting the HTTP server *inside* the sync callback:
    server.listen(PORT, HOST, () => {
      console.log(`🚀 Server is running on ${HOST}:${PORT}`);
      console.log(`🔌 WebSocket server is ready`);
      console.log(
        `📱 Mobile devices can connect to: http://************:${PORT}`
      );
    });
  })
  .catch((err) => {
    console.error("❌ Failed to sync database:", err);
    // Start server anyway for debugging
    server.listen(PORT, HOST, () => {
      console.log(
        `🚀 Server is running on ${HOST}:${PORT} (without database sync)`
      );
      console.log(`🔌 WebSocket server is ready`);
      console.log(
        `📱 Mobile devices can connect to: http://************:${PORT}`
      );
    });
  });
