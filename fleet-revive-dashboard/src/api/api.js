// src/api.js
import axios from "axios";

// Base Axios instance with environment-based URL
const baseURL = "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com/api";

// Debug logging
console.log("🌐 Dashboard API Configuration:");
console.log("🌐 Environment DEV:", import.meta.env.DEV);
console.log("🌐 Use Live API:", import.meta.env.VITE_USE_LIVE_API);
console.log("🌐 Base URL:", baseURL);

const api = axios.create({
  baseURL: baseURL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Attach token to each request if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default api;
