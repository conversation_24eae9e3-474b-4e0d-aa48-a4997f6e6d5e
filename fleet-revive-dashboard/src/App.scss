/* Modern Lightweight Dashboard Theme */
:root {
  --bs-body-font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  --bs-font-weight: 400;
  --bs-line-height: 1.6;
  --bs-font-smoothing: antialiased;

  /* Light Theme Colors */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;

  /* Background Colors */
  --bg-color: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-card: #ffffff;
  --bg-modal-color: #ffffff;

  /* Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;

  /* Border Colors */
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
}

[data-theme="dark"] {
  --bg-color: #0f172a;
  --bg-secondary: #1e293b;
  --bg-card: #1e293b;
  --bg-modal-color: #1e293b;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;

  --border-color: #334155;
  --border-light: #475569;
}
/* Global Styles */
* {
  box-sizing: border-box;
}

html,
body,
#root {
  font-family: var(--bs-body-font-family) !important;
  font-weight: var(--bs-font-weight);
  line-height: var(--bs-line-height);
  -webkit-font-smoothing: var(--bs-font-smoothing);
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

body {
  background-color: var(--bg-color);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

#root {
  margin: 0;
  width: 100%;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.4;
  margin: 0 0 1rem 0;
}

p {
  color: var(--text-secondary);
  margin: 0 0 1rem 0;
}

/* Links */
a {
  text-decoration: none;
  color: var(--primary-color);
  transition: color 0.2s ease;

  &:hover {
    color: var(--primary-hover);
  }
}

/* Buttons */
.btn {
  border: none;
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  &.btn-primary {
    background-color: var(--primary-color);
    color: white;

    &:hover {
      background-color: var(--primary-hover);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }

  &.btn-secondary {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);

    &:hover {
      background-color: var(--border-light);
    }
  }
}

/* Cards */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
}

/* Form Elements */
.form-control {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0.75rem;
  background-color: var(--bg-color);
  color: var(--text-primary);
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
  }
}

/* Utilities */
.text-muted {
  color: var(--text-muted) !important;
}

.border-light {
  border-color: var(--border-light) !important;
}
.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px; /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;

  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;
  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;

  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;

  /* Support for IE. */
  font-feature-settings: "liga";
}
