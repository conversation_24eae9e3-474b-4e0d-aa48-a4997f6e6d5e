import React, { useState, useEffect, useContext } from "react";
import { motion } from "framer-motion";
import { AuthContext } from "../../auth/AuthContext";
import { AlertContext } from "../../components/AlertContext";
import StatCard from "../../components/ui/StatCard";
import api from "../../api/api";
import "../../css/Home.scss";

const Home = () => {
  const { role, company } = useContext(AuthContext);
  const { showAlert } = useContext(AlertContext);
  const [stats, setStats] = useState({
    ongoingJobs: 0,
    activeUsers: 0,
    finishedJobs: 0,
    totalRequests: 0,
    loading: true
  });

  useEffect(() => {
    fetchDashboardStats();
    const interval = setInterval(fetchDashboardStats, 30000);
    return () => clearInterval(interval);
  }, [role, company]);

  const fetchDashboardStats = async () => {
    try {
      setStats(prev => ({ ...prev, loading: true }));
      
      if (role === "transport") {
        await fetchTransportStats();
      } else if (role === "repair") {
        await fetchRepairStats();
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      if (showAlert) {
        showAlert("Failed to fetch dashboard statistics", "error");
      }
    } finally {
      setStats(prev => ({ ...prev, loading: false }));
    }
  };

  const fetchTransportStats = async () => {
    try {
      // For now, use mock data until API is fully connected
      setStats({
        ongoingJobs: 12,
        activeUsers: 42,
        finishedJobs: 8,
        totalRequests: 20,
        loading: false
      });
    } catch (error) {
      console.error("Error fetching transport stats:", error);
      throw error;
    }
  };

  const fetchRepairStats = async () => {
    try {
      // For now, use mock data until API is fully connected
      setStats({
        ongoingJobs: 5,
        activeUsers: 15,
        finishedJobs: 23,
        totalRequests: 28,
        loading: false
      });
    } catch (error) {
      console.error("Error fetching repair stats:", error);
      throw error;
    }
  };

  if (stats.loading) {
    return (
      <div className="modern-loading">
        <motion.div
          className="loading-spinner"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          Loading dashboard...
        </motion.p>
      </div>
    );
  }

  return (
    <div className="modern-dashboard">
      {/* Background Effects */}
      <div className="dashboard-background">
        <div className="gradient-orb orb-1" />
        <div className="gradient-orb orb-2" />
        <div className="gradient-orb orb-3" />
      </div>

      {/* Header */}
      <motion.div
        className="dashboard-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="header-content">
          <div>
            <h1 className="dashboard-title">Dashboard Overview</h1>
            <p className="dashboard-subtitle">
              Real-time insights and analytics for your fleet operations
            </p>
          </div>
          <div className="company-card">
            <div className="company-info">
              <div className="company-avatar">
                {role === 'transport' ? '🚛' : '🔧'}
              </div>
              <div>
                <h3 className="company-name">{company?.name || 'Company'}</h3>
                <p className="company-type">
                  {role === 'transport' ? 'Transport Company' : 'Repair Company'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Statistics Grid */}
      <motion.div
        className="stats-grid"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <StatCard
          title="Active Jobs"
          value={stats.ongoingJobs}
          subtitle="Currently in progress"
          icon="🚛"
          color="blue"
          trend="up"
          trendValue="+12%"
        />

        <StatCard
          title={`Active ${role === 'transport' ? 'Drivers' : 'Mechanics'}`}
          value={stats.activeUsers}
          subtitle="Currently available"
          icon={role === 'transport' ? '👨‍💼' : '🔧'}
          color="green"
          trend="up"
          trendValue="+5%"
        />

        <StatCard
          title="Completed Jobs"
          value={stats.finishedJobs}
          subtitle="Successfully finished"
          icon="✅"
          color="purple"
          trend="up"
          trendValue="+8%"
        />

        <StatCard
          title="Total Requests"
          value={stats.totalRequests}
          subtitle="All time requests"
          icon="📊"
          color="orange"
          trend="up"
          trendValue="+15%"
        />
      </motion.div>

      {/* Quick Stats & Analytics */}
      <div className="dashboard-grid">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <GlassCard className="analytics-card">
            <div className="card-header">
              <h3>Performance Analytics</h3>
              <div className="auto-refresh">
                <motion.div
                  className="refresh-icon"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  🔄
                </motion.div>
                <span>Auto-refresh</span>
              </div>
            </div>
            <div className="analytics-content">
              <div className="metric">
                <span className="metric-label">Completion Rate</span>
                <div className="metric-value">
                  <span className="value">
                    {stats.totalRequests > 0
                      ? `${Math.round((stats.finishedJobs / stats.totalRequests) * 100)}%`
                      : '0%'
                    }
                  </span>
                  <div className="progress-bar">
                    <motion.div
                      className="progress-fill"
                      initial={{ width: 0 }}
                      animate={{
                        width: stats.totalRequests > 0
                          ? `${Math.round((stats.finishedJobs / stats.totalRequests) * 100)}%`
                          : '0%'
                      }}
                      transition={{ duration: 1, delay: 0.5 }}
                    />
                  </div>
                </div>
              </div>

              <div className="metric">
                <span className="metric-label">System Status</span>
                <div className="status-indicator">
                  <motion.div
                    className="status-dot active"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                  <span>All Systems Operational</span>
                </div>
              </div>

              <div className="metric">
                <span className="metric-label">Company Type</span>
                <span className="company-badge">
                  {role === 'transport' ? '🚛 Transport' : '🔧 Repair'}
                </span>
              </div>
            </div>
          </GlassCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <ActivityFeed />
        </motion.div>
      </div>
    </div>
  );
};

export default Home;
