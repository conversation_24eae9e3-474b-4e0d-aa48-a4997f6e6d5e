/* Modern Layout Styles */
.layout {
  height: 100vh;
  position: relative;
  background-color: var(--bg-secondary);

  .sidebar-container {
    position: fixed;
    left: 0;
    top: 0;
    width: 260px;
    height: 100vh;
    z-index: 1000;
  }

  .main-content {
    display: flex;
    flex-direction: column;
    margin-left: 260px;
    width: calc(100% - 260px);
    height: 100vh;
    background-color: var(--bg-secondary);

    .page-content {
      flex: 1;
      overflow-y: auto;
      padding: 0;

      // Custom scrollbar
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--border-color);
        border-radius: 3px;

        &:hover {
          background: var(--text-muted);
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .layout {
    .sidebar-container {
      width: 240px;
    }

    .main-content {
      margin-left: 240px;
      width: calc(100% - 240px);
    }
  }
}

@media (max-width: 768px) {
  .layout {
    .sidebar-container {
      transform: translateX(-100%);
      transition: transform 0.3s ease;

      &.mobile-open {
        transform: translateX(0);
      }
    }

    .main-content {
      margin-left: 0;
      width: 100%;
    }
  }
}
