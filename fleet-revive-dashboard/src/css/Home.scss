// Modern Dashboard Home Styles
.dashboard-home {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--bg-secondary);
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 2rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 1.5rem;

    .dashboard-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--text-primary);
      margin: 0 0 0.5rem 0;
      line-height: 1.2;
    }

    .dashboard-subtitle {
      font-size: 1rem;
      color: var(--text-secondary);
      margin: 0;
      line-height: 1.5;
    }

    .company-card {
      background: var(--bg-card);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      padding: 1.5rem;
      box-shadow: var(--shadow-sm);
      min-width: 280px;

      .company-info {
        display: flex;
        align-items: center;
        gap: 1rem;

        .company-avatar {
          width: 48px;
          height: 48px;
          border-radius: var(--radius-md);
          background: var(--primary-color);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          color: white;
        }

        .company-name {
          font-size: 1.125rem;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 0.25rem 0;
        }

        .company-type {
          font-size: 0.875rem;
          color: var(--text-muted);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          font-weight: 500;
          margin: 0;
        }
      }
    }
  }
}

// Statistics Grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

// Activity Section
.activity-section {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }

    .view-all-btn {
      color: var(--primary-color);
      font-size: 0.875rem;
      font-weight: 500;
      text-decoration: none;
      padding: 0.5rem 1rem;
      border-radius: var(--radius-md);
      transition: all 0.2s ease;

      &:hover {
        background: rgb(59 130 246 / 0.1);
      }
    }
  }
}

// Quick Actions
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;

  .action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.25rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      border-color: var(--primary-color);
      color: var(--primary-color);
    }

    .action-icon {
      font-size: 1.25rem;
    }

    .action-text {
      font-size: 0.875rem;
    }
  }
}

// Analytics Card
.analytics-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  margin-bottom: 2rem;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }

    .auto-refresh {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--text-muted);
      font-size: 0.875rem;

      .refresh-icon {
        width: 16px;
        height: 16px;
        border: 2px solid var(--primary-color);
        border-top-color: transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  .performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;

    .metric-item {
      text-align: center;
      padding: 1rem;
      background: var(--bg-secondary);
      border-radius: var(--radius-md);

      .metric-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
      }

      .metric-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-bottom: 0.25rem;
      }

      .metric-change {
        font-size: 0.75rem;
        font-weight: 500;

        &.positive {
          color: var(--success-color);
        }

        &.negative {
          color: var(--danger-color);
        }
      }
    }
  }

  .company-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
  }

  .activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .activity-item {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem;
      background: var(--bg-secondary);
      border-radius: var(--radius-md);
      transition: all 0.2s ease;

      &:hover {
        background: var(--border-light);
      }

      .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-md);
        background: var(--bg-card);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        flex-shrink: 0;
      }

      .activity-content {
        flex: 1;

        .activity-title {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--text-primary);
          margin: 0 0 0.25rem 0;
        }

        .activity-time {
          font-size: 0.75rem;
          color: var(--text-muted);
          margin: 0;
        }
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .dashboard-home {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .dashboard-home {
    padding: 1rem;
  }

  .dashboard-header .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;

    .company-card {
      min-width: auto;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 20px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color, #e0e0e0);
    border-top: 4px solid var(--main-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  p {
    color: var(--secondary-text-color, #666);
    font-size: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.statistics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .box {
    background: linear-gradient(135deg, var(--main-light-color), #ffffff);
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid var(--border-color, #e0e0e0);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .box-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      .icon {
        font-size: 24px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12px;
      }

      p {
        font-size: 14px;
        font-weight: 600;
        color: var(--main-color);
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .bars-numbers {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 12px;

      .vertical-bar {
        width: 8px;
        height: 40px;
        border-radius: 4px;
        background: linear-gradient(to top, var(--main-color), rgba(255, 255, 255, 0.3));
      }

      .stat-number {
        margin: 0;
        font-size: 32px;
        font-weight: 700;
        color: var(--main-text-color);
        line-height: 1;
      }
    }

    .box-footer {
      small {
        color: var(--secondary-text-color, #666);
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}

.quick-stats {
  display: flex;
  justify-content: space-around;
  background: var(--main-light-color);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  border: 1px solid var(--border-color, #e0e0e0);

  .quick-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .label {
      font-size: 12px;
      color: var(--secondary-text-color, #666);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 600;
    }

    .value {
      font-size: 18px;
      font-weight: 700;
      color: var(--main-color);
    }
  }
}

.recent-activity {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, #e0e0e0);
  margin-bottom: 20px;

  h3 {
    margin: 0 0 20px 0;
    font-size: 20px;
    color: var(--main-text-color);
    font-weight: 600;
  }

  .activity-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .activity-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    background: var(--main-light-color);
    border-radius: 8px;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.02);
    }

    .activity-icon {
      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-top: 4px;
      }
    }

    .activity-content {
      flex: 1;

      .activity-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--main-text-color);
        margin-bottom: 4px;
        text-transform: capitalize;
      }

      .activity-meta {
        display: flex;
        gap: 12px;
        margin-bottom: 8px;

        .status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 12px;
          background: rgba(0, 0, 0, 0.1);
          color: var(--main-text-color);
          text-transform: capitalize;
          font-weight: 500;
        }

        .date {
          font-size: 12px;
          color: var(--secondary-text-color, #666);
        }
      }

      .activity-description {
        font-size: 14px;
        color: var(--secondary-text-color, #666);
        line-height: 1.4;
      }
    }
  }

  .no-activity {
    text-align: center;
    padding: 40px 20px;
    color: var(--secondary-text-color, #666);

    p {
      margin: 0;
      font-size: 16px;
    }
  }
}

.auto-refresh-indicator {
  text-align: center;
  padding: 10px;

  small {
    color: var(--secondary-text-color, #666);
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-home {
    padding: 16px;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .company-info {
      align-items: flex-start;
    }
  }

  .statistics {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;

    .box {
      padding: 20px;

      .box-header .icon {
        width: 32px;
        height: 32px;
        font-size: 20px;
      }

      .bars-numbers .stat-number {
        font-size: 28px;
      }
    }
  }

  .quick-stats {
    flex-direction: column;
    gap: 16px;

    .quick-stat {
      flex-direction: row;
      justify-content: space-between;
    }
  }

  .activity-item {
    flex-direction: column;
    gap: 12px;

    .activity-icon {
      align-self: flex-start;
    }
  }
}
