// Modern Lightweight Sidebar
.modern-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 260px;
  height: 100vh;
  background-color: var(--bg-card);
  border-right: 1px solid var(--border-color);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  padding: 1.5rem 1rem;
  overflow-y: auto;
  transition: all 0.3s ease;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;

    &:hover {
      background: var(--text-muted);
    }
  }
}

// Header Section
.sidebar-header {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-light);

  .brand-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--bg-secondary);
    }

    .logo-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-md);
      background: var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      color: white;
      box-shadow: var(--shadow-sm);
    }

    .brand-text {
      h3 {
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 700;
        margin: 0;
        line-height: 1.2;
      }

      span {
        color: var(--text-muted);
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
      }
    }
  }
}

// Navigation
.sidebar-nav {
  flex: 1;

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .nav-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--bg-secondary);
      color: var(--text-primary);
      transform: translateX(2px);

      .nav-icon {
        transform: scale(1.05);
      }
    }

    &.active {
      background-color: rgb(59 130 246 / 0.1);
      color: var(--primary-color);
      font-weight: 600;

      .nav-icon {
        color: var(--primary-color);
      }

      .active-indicator {
        position: absolute;
        right: 0.75rem;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: var(--primary-color);
      }
    }

    .nav-icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.1rem;
      transition: all 0.2s ease;
    }

    .nav-label {
      font-size: 0.875rem;
      font-weight: inherit;
      letter-spacing: 0.01em;
    }
  }
}

// Footer Section
.sidebar-footer {
  margin-top: 2rem;

  .company-profile {
    padding: 1.25rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .profile-avatar {
      width: 45px;
      height: 45px;
      border-radius: 50%;
      background: linear-gradient(135deg, #10b981, #3b82f6);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 700;
      font-size: 1.1rem;
      box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
    }

    .profile-info {
      flex: 1;

      h4 {
        color: white;
        font-size: 0.95rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
      }

      span {
        color: rgba(255, 255, 255, 0.5);
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .profile-menu {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1rem;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .modern-sidebar {
    width: 260px;
    padding: 1.5rem 1rem;
  }

  .sidebar-header .brand-logo {
    .logo-icon {
      width: 45px;
      height: 45px;
      font-size: 1.3rem;
    }

    .brand-text h3 {
      font-size: 1.2rem;
    }
  }
}

@media (max-width: 768px) {
  .modern-sidebar {
    width: 240px;
    padding: 1rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.open {
      transform: translateX(0);
    }
  }

  .sidebar-nav .nav-item {
    padding: 0.875rem 1rem;
    
    .nav-label {
      font-size: 0.9rem;
    }
  }

  .sidebar-footer .company-profile {
    padding: 1rem;
    
    .profile-avatar {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }
  }
}

// Mobile overlay
@media (max-width: 768px) {
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.open {
      opacity: 1;
      visibility: visible;
    }
  }
}
