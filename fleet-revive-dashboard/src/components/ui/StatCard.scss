// Modern Statistics Card Styles
.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover::before {
    opacity: 1;
  }
}

.stat-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.stat-card__icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.stat-card__trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;

  &--up {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
  }

  &--down {
    background: rgb(239 68 68 / 0.1);
    color: var(--danger-color);
  }

  &--neutral {
    background: var(--bg-secondary);
    color: var(--text-secondary);
  }

  .trend-icon {
    font-size: 0.875rem;
  }
}

.stat-card__content {
  .stat-card__value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
    margin-bottom: 0.5rem;
  }

  .stat-card__title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
  }

  .stat-card__subtitle {
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1.4;
  }
}

// Color Variants
.stat-card--blue {
  &::before {
    background: var(--primary-color);
  }

  .stat-card__icon {
    background: rgb(59 130 246 / 0.1);
    color: var(--primary-color);
  }

  &:hover {
    border-color: var(--primary-color);
  }
}

.stat-card--green {
  &::before {
    background: var(--success-color);
  }

  .stat-card__icon {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
  }

  &:hover {
    border-color: var(--success-color);
  }
}

.stat-card--purple {
  &::before {
    background: #8b5cf6;
  }

  .stat-card__icon {
    background: rgb(139 92 246 / 0.1);
    color: #8b5cf6;
  }

  &:hover {
    border-color: #8b5cf6;
  }
}

.stat-card--orange {
  &::before {
    background: var(--warning-color);
  }

  .stat-card__icon {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-color);
  }

  &:hover {
    border-color: var(--warning-color);
  }
}

.stat-card--red {
  &::before {
    background: var(--danger-color);
  }

  .stat-card__icon {
    background: rgb(239 68 68 / 0.1);
    color: var(--danger-color);
  }

  &:hover {
    border-color: var(--danger-color);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .stat-card {
    padding: 1.25rem;

    .stat-card__icon {
      width: 40px;
      height: 40px;
      font-size: 1.25rem;
    }

    .stat-card__content .stat-card__value {
      font-size: 1.75rem;
    }
  }
}
