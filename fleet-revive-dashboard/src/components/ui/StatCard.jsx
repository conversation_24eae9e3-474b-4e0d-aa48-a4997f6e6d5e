// Modern Statistics Card Component
import React from 'react';
import { motion } from 'framer-motion';
import './StatCard.scss';

const StatCard = ({ 
  title, 
  value, 
  subtitle, 
  icon, 
  color = 'blue', 
  trend, 
  trendValue,
  className = '' 
}) => {
  const colorClasses = {
    blue: 'stat-card--blue',
    green: 'stat-card--green',
    purple: 'stat-card--purple',
    orange: 'stat-card--orange',
    red: 'stat-card--red'
  };

  return (
    <motion.div
      className={`stat-card ${colorClasses[color]} ${className}`}
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div className="stat-card__header">
        <div className="stat-card__icon">
          {icon}
        </div>
        {trend && (
          <div className={`stat-card__trend stat-card__trend--${trend}`}>
            <span className="trend-icon">
              {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'}
            </span>
            <span className="trend-value">{trendValue}</span>
          </div>
        )}
      </div>

      <div className="stat-card__content">
        <div className="stat-card__value">{value}</div>
        <div className="stat-card__title">{title}</div>
        {subtitle && (
          <div className="stat-card__subtitle">{subtitle}</div>
        )}
      </div>
    </motion.div>
  );
};

export default StatCard;
