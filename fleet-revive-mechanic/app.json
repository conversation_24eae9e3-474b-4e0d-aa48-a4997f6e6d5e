{"expo": {"name": "fleet-revive-mechanic", "slug": "fleet-revive-mechanic", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.nirmal0210.fleetrevivemechanic", "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSLocationWhenInUseUsageDescription": "FleetRevive Mechanic needs access to your location to provide navigation and location-based services.", "NSLocationAlwaysUsageDescription": "FleetRevive Mechanic needs access to your location in the background for route tracking and updates.", "NSUserTrackingUsageDescription": "FleetRevive Mechanic uses this permission to send notifications and keep you updated with service requests.", "UIBackgroundModes": ["fetch", "remote-notification"]}, "config": {"googleMapsApiKey": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.nirmal0210.fleetrevivemechanic", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"], "googleServicesFile": "./google-services.json", "config": {"googleMaps": {"apiKey": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0"}}}, "web": {"apiUrl": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "favicon": "./assets/favicon.png"}, "extra": {"apiUrl": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "productionApiUrl": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "socketUrl": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "eas": {"projectId": "************************************"}}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location.", "locationAlwaysPermission": "Allow $(PRODUCT_NAME) to use your location.", "locationWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "defaultChannel": "default"}], "expo-localization", ["expo-maps", {"googleMapsApiKey": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0"}]], "notification": {"icon": "./assets/notification-icon.png", "color": "#ffffff"}}}