import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '../api/api';
import messaging from '@react-native-firebase/messaging';
import firebase from '@react-native-firebase/app';

// Background message handler function - will be set up after Firebase is initialized
const setupBackgroundMessageHandler = () => {
  try {
    // Set up background message handler for when app is completely closed
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('🔔 FCM Background message received:', remoteMessage);

      // Handle background message
      // This runs when the app is completely closed
      // You can perform background tasks here like updating local storage

      if (remoteMessage.data?.type === 'new_service_request') {
        console.log('📱 New service request received in background');
        // Could store notification data locally for when app opens
      }
    });
    console.log('🔔 Background message handler set up successfully');
  } catch (error) {
    console.log('🔔 Failed to set up background message handler:', error.message);
  }
};

class FCMService {
  constructor() {
    this.token = null;
    this.isInitialized = false;
    this.instanceId = Math.random().toString(36).substr(2, 9);
    this.pendingInitialNotification = null;
    this.processedNotifications = new Set();
    this.notificationTimeout = 5 * 60 * 1000; // 5 minutes
    console.log('🏗️ FCMService instance created with ID:', this.instanceId);
  }

  async initialize() {
    console.log('🚀 FCM Service initialize() called on instance:', this.instanceId, 'isInitialized:', this.isInitialized);
    if (this.isInitialized) return;

    try {
      // Wait a bit to ensure Firebase is ready
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if Firebase is initialized, if not try to initialize it manually
      if (!firebase.apps.length) {
        console.log('🔥 Firebase not initialized, attempting manual initialization...');

        try {
          // Try to initialize Firebase manually with config from GoogleService-Info.plist
          const firebaseConfig = {
            apiKey: "AIzaSyCe6G_KsEObkrnkCNueC7NO5l3LbLjzLmU",
            authDomain: "fleet-revive-app.firebaseapp.com",
            databaseURL: "https://fleet-revive-app-default-rtdb.firebaseio.com",
            projectId: "fleet-revive-app",
            storageBucket: "fleet-revive-app.firebasestorage.app",
            messagingSenderId: "1046250054230",
            appId: "1:1046250054230:ios:1d5d6bde949d062afd51f7"
          };

          await firebase.initializeApp(firebaseConfig);
          console.log('🔥 Firebase manually initialized successfully');
        } catch (initError) {
          console.error('🔥 Failed to manually initialize Firebase:', initError.message);
          throw new Error('Firebase initialization failed');
        }
      }

      console.log('🔥 Firebase is initialized, apps:', firebase.apps.length);

      // Configure notification behavior
      await Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      });

      // Request permissions
      await this.requestPermissions();

      // Get FCM token with retry logic
      await this.generateFCMToken();

      // Set up background message handler
      setupBackgroundMessageHandler();

      // Set up notification listeners
      console.log('🔔 About to call setupNotificationListeners...');
      this.setupNotificationListeners();
      console.log('🔔 setupNotificationListeners completed');

      this.isInitialized = true;
      console.log('FCM Service initialized successfully on instance:', this.instanceId);
    } catch (error) {
      console.error('Error initializing FCM Service:', error);
      // Retry initialization after a delay
      setTimeout(() => {
        console.log('Retrying FCM Service initialization...');
        this.initialize();
      }, 3000);
    }
  }

  async requestPermissions() {
    console.log('🔔 Requesting notification permissions...');

    if (!Device.isDevice) {
      console.log('⚠️ Must use physical device for Push Notifications');
      return false;
    }

    try {
      // Request Expo notification permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      console.log('📱 Current Expo notification status:', existingStatus);

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
        console.log('📱 New Expo notification status:', status);
      }

      // Also request Firebase messaging permissions (iOS)
      if (Platform.OS === 'ios') {
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        console.log('🔥 Firebase messaging permission:', enabled ? 'granted' : 'denied');

        if (!enabled) {
          console.log('❌ Firebase messaging permission denied');
          return false;
        }
      }

      if (finalStatus !== 'granted') {
        console.log('❌ Failed to get push notification permissions!');
        return false;
      }

      console.log('✅ Push notification permissions granted');
      return true;
    } catch (error) {
      console.error('❌ Error requesting permissions:', error);
      return false;
    }
  }

  // Generate FCM token (alias for getFCMToken for consistency)
  async generateFCMToken() {
    return await this.getFCMToken();
  }

  async getFCMToken() {
    try {
      // Only iOS simulator cannot receive FCM notifications
      // Android emulators CAN receive real FCM notifications
      if (!Device.isDevice && Platform.OS === 'ios') {
        console.log('Must use physical device for Push Notifications on iOS');
        // Return a development token for testing
        const devToken = 'DEV_TOKEN_' + Date.now();
        this.token = devToken;
        return devToken;
      }

      if (!Device.isDevice && Platform.OS === 'android') {
        console.log('🤖 Android emulator detected - FCM notifications should work');
      }

      // Use Firebase FCM instead of Expo tokens
      // Request permission for iOS
      if (Platform.OS === 'ios') {
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (!enabled) {
          console.log('Push notification permission denied');
          return null;
        }
      }

      // Get FCM token with retry logic
      let fcmToken = null;
      let retries = 3;

      while (retries > 0 && !fcmToken) {
        try {
          fcmToken = await messaging().getToken();
          if (fcmToken) break;
        } catch (tokenError) {
          console.log(`FCM token generation attempt failed (${4 - retries}/3):`, tokenError.message);
          retries--;
          if (retries > 0) {
            await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retry
          }
        }
      }

      if (fcmToken) {
        this.token = fcmToken;
        console.log('FCM Token:', this.token);

        // Store token locally
        await AsyncStorage.setItem('fcm_token', this.token);

        // Send token to backend if user is authenticated
        try {
          const { store } = require('../store/store');
          const state = store.getState();
          if (state.auth.isLoggedIn && state.auth.token) {
            await this.sendTokenToBackend();
          }
        } catch (backendError) {
          console.log('Failed to send token to backend:', backendError.message);
        }
      } else {
        console.log('Failed to get FCM token after 3 attempts');
        return null;
      }

      return this.token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  async sendTokenToBackend() {
    try {
      if (!this.token) {
        console.log('No FCM token available');
        return;
      }

      // Check if user is authenticated
      const token = await AsyncStorage.getItem('token');
      const userStr = await AsyncStorage.getItem('user');

      if (!token || !userStr) {
        console.log('User not authenticated, skipping token update');
        return;
      }

      const user = JSON.parse(userStr);

      // Send FCM token directly to backend with maximum expiry
      await this.updateFCMToken(user.id, this.token);
      console.log('FCM token sent to backend successfully');
    } catch (error) {
      console.error('Error sending FCM token to backend:', error);
    }
  }

  // Update FCM token on backend with maximum expiry (direct API call)
  async updateFCMToken(userId, fcmToken) {
    try {
      // Set FCM token expiry to maximum possible (10 years from now)
      const maxExpiryDate = new Date();
      maxExpiryDate.setFullYear(maxExpiryDate.getFullYear() + 10); // 10 years maximum

      const response = await api.put('/mechanics/fcm-token', {
        user_id: userId,
        fcm_token: fcmToken,
        expires_at: maxExpiryDate.toISOString(), // Maximum expiry date
        updated_at: new Date().toISOString(),
      });

      console.log('✅ FCM token updated on backend with max expiry:', response.data);
      console.log('🗓️ Token expires on:', maxExpiryDate.toLocaleDateString());

      // Store token expiry locally for reference
      await AsyncStorage.setItem('fcm_token_expiry', maxExpiryDate.toISOString());

      return response.data;
    } catch (error) {
      console.error('❌ Error updating FCM token on backend:', error);
      throw error;
    }
  }

  // Check if FCM token needs refresh and refresh if necessary
  async checkAndRefreshToken() {
    try {
      const storedExpiry = await AsyncStorage.getItem('fcm_token_expiry');

      if (!storedExpiry) {
        console.log('🔄 No FCM token expiry found, generating new token...');
        await this.generateFCMToken();
        return;
      }

      const expiryDate = new Date(storedExpiry);
      const now = new Date();
      const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));

      console.log(`🗓️ FCM token expires in ${daysUntilExpiry} days`);

      // Refresh token if it expires within 30 days
      if (daysUntilExpiry <= 30) {
        console.log('🔄 FCM token expiring soon, refreshing...');
        await this.generateFCMToken();
      } else {
        console.log('✅ FCM token is still valid');
      }
    } catch (error) {
      console.error('❌ Error checking FCM token expiry:', error);
      // If there's an error, generate a new token to be safe
      await this.generateFCMToken();
    }
  }

  // Setup periodic token refresh (call this during app initialization)
  setupTokenRefresh() {
    // Check token immediately
    this.checkAndRefreshToken();

    // Set up periodic check every 24 hours
    this.tokenRefreshInterval = setInterval(() => {
      this.checkAndRefreshToken();
    }, 24 * 60 * 60 * 1000); // 24 hours
  }

  // Cleanup method
  cleanup() {
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }

  setupNotificationListeners() {
    console.log('🔔 Setting up notification listeners...');
    // Handle notification received while app is in foreground
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('Expo Notification received:', notification);
        this.handleNotificationReceived(notification);
      }
    );

    // Handle notification tapped
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('Expo Notification tapped:', response);
        this.handleNotificationTapped(response);
      }
    );

    // Set up Firebase messaging handlers for FCM messages
    this.setupFirebaseMessageHandlers();
  }

  setupFirebaseMessageHandlers() {
    console.log('🔔 Setting up Firebase messaging handlers...');
    try {
      // Handle messages received while app is in foreground
      this.unsubscribeOnMessage = messaging().onMessage(async (remoteMessage) => {
        console.log('🔔 FCM Message received in foreground:', remoteMessage);

        // Show local notification for foreground messages
        if (remoteMessage.notification) {
          await this.scheduleLocalNotification(
            remoteMessage.notification.title || 'New Notification',
            remoteMessage.notification.body || 'You have a new notification',
            remoteMessage.data || {}
          );
        }

        // Handle the message data
        this.handleFCMMessage(remoteMessage);
      });

      // Handle messages received when app is opened from background/quit state
      messaging().getInitialNotification().then((remoteMessage) => {
        if (remoteMessage) {
          console.log('🔔 FCM Initial notification received:', remoteMessage);
          // Store the initial notification and handle it after app is fully loaded
          this.pendingInitialNotification = remoteMessage;
          this.handleInitialNotificationWithDelay();
        }
      });

      // Handle messages received when app is in background and user taps notification
      this.unsubscribeOnNotificationOpenedApp = messaging().onNotificationOpenedApp((remoteMessage) => {
        console.log('🔔 FCM Notification opened app:', remoteMessage);
        this.handleFCMMessage(remoteMessage);
      });

      console.log('✅ Firebase messaging handlers set up successfully');
    } catch (error) {
      console.error('❌ Error setting up Firebase messaging handlers:', error);
    }
  }

  handleInitialNotificationWithDelay() {
    // Wait for app to be fully loaded before processing initial notification
    // This ensures Redux store and components are ready
    setTimeout(() => {
      if (this.pendingInitialNotification) {
        console.log('🔔 Processing delayed initial notification:', this.pendingInitialNotification);
        this.handleFCMMessage(this.pendingInitialNotification);
        this.pendingInitialNotification = null;
      }
    }, 2000); // 2 second delay to ensure app is fully loaded
  }

  // Method to manually process pending initial notification (can be called from HomeScreen)
  processPendingInitialNotification() {
    if (this.pendingInitialNotification) {
      console.log('🔔 Manually processing pending initial notification:', this.pendingInitialNotification);
      this.handleFCMMessage(this.pendingInitialNotification);
      this.pendingInitialNotification = null;
      return true;
    }
    return false;
  }

  // Generate unique notification ID for deduplication
  generateNotificationId(data) {
    if (!data) {
      console.warn('🔔 FCM: generateNotificationId called with undefined data');
      return `unknown_${Date.now()}`;
    }

    const type = data.type || 'unknown';
    const serviceId = data.service_request_id || data.serviceRequestId || 'unknown';
    const timestamp = data.timestamp || Date.now();

    return `${type}_${serviceId}_${timestamp}`;
  }

  // Check if notification was already processed
  isNotificationProcessed(notificationId) {
    return this.processedNotifications.has(notificationId);
  }

  // Mark notification as processed
  markNotificationProcessed(notificationId) {
    this.processedNotifications.add(notificationId);

    // Clean up old notifications after timeout
    setTimeout(() => {
      this.processedNotifications.delete(notificationId);
    }, this.notificationTimeout);
  }

  handleFCMMessage(remoteMessage) {
    console.log('🔔 Handling FCM message:', remoteMessage);

    const { data, notification } = remoteMessage;

    // Generate unique ID for deduplication
    const notificationId = this.generateNotificationId(data);

    // Check if we already processed this notification
    if (this.isNotificationProcessed(notificationId)) {
      console.log('🔄 Skipping duplicate FCM message:', notificationId);
      return;
    }

    // Mark as processed
    this.markNotificationProcessed(notificationId);

    // Handle different message types based on data
    if (data?.type) {
      switch (data.type) {
        case 'new_service_request':
          this.handleNewServiceRequest(data);
          break;
        case 'service_request_update':
          this.handleServiceRequestUpdate(data);
          break;
        case 'mechanic_assigned':
          this.handleMechanicAssigned(data);
          break;
        case 'service_completed':
          this.handleServiceCompleted(data);
          break;
        default:
          console.log('Unknown FCM message type:', data.type);
      }
    } else {
      console.log('FCM message without type data:', remoteMessage);
    }
  }

  handleNewServiceRequest(data) {
    console.log('🚛 New service request received via FCM:', data);

    try {
      // Import serviceNotificationManager to handle the notification
      const serviceNotificationManager = require('./serviceNotificationManager').default;

      // Reconstruct service request object from FCM data
      const serviceRequest = {
        id: data.service_request_id,
        issue_type: data.issue_type,
        issue_description: data.issue_description,
        driver_name: data.driver_name,
        driver_phone: data.driver_phone,
        truck_info: data.truck_info,
        latitude: data.latitude ? parseFloat(data.latitude) : null,
        longitude: data.longitude ? parseFloat(data.longitude) : null,
        created_at: new Date().toISOString(),
        urgency: data.urgency || 'normal',
        // Add any other fields that might be in the data
        ...data
      };

      console.log('🔧 Reconstructed service request from FCM data:', serviceRequest);

      // Validate that we have the essential fields
      if (!serviceRequest.id) {
        console.error('❌ Service request ID missing from FCM data:', data);
        return;
      }

      // Pass to service notification manager for proper handling
      serviceNotificationManager.handleNewServiceRequest(serviceRequest);

      console.log('✅ Service request passed to notification manager via FCM');
    } catch (error) {
      console.error('❌ Error handling new service request:', error);
    }
  }

  handleNotificationReceived(notification) {
    if (!notification) {
      console.warn('🔔 FCM: handleNotificationReceived called with undefined notification');
      return;
    }

    const { data } = notification;

    // Generate unique ID for deduplication
    const notificationId = this.generateNotificationId(data);

    // Check if we already processed this notification
    if (this.isNotificationProcessed(notificationId)) {
      console.log('🔄 Skipping duplicate Expo notification:', notificationId);
      return;
    }

    // Mark as processed
    this.markNotificationProcessed(notificationId);

    // Handle different notification types
    switch (data?.type) {
      case 'service_request_update':
        this.handleServiceRequestUpdate(data);
        break;
      case 'mechanic_assigned':
        this.handleMechanicAssigned(data);
        break;
      case 'service_completed':
        this.handleServiceCompleted(data);
        break;
      default:
        console.log('Unknown notification type:', data?.type);
    }
  }

  handleNotificationTapped(response) {
    const { data } = response.notification.request.content;
    
    // Navigate based on notification data
    // This would typically use your navigation service
    console.log('Handle notification tap navigation:', data);
  }

  handleServiceRequestUpdate(data) {
    // Update service request in Redux store
    console.log('Service request update:', data);
  }

  handleMechanicAssigned(data) {
    // Handle mechanic assignment
    console.log('Mechanic assigned:', data);
  }

  handleServiceCompleted(data) {
    // Handle service completion
    console.log('Service completed:', data);
  }

  // Notification tone configuration
  static NOTIFICATION_TONES = {
    INFORMATIVE: {
      sound: 'default',
      priority: 'default',
      vibrationPattern: [0, 250, 250, 250],
    },
    URGENT: {
      sound: 'default',
      priority: 'high',
      vibrationPattern: [0, 500, 200, 500],
    },
    SUCCESS: {
      sound: 'default',
      priority: 'default',
      vibrationPattern: [0, 200, 100, 200],
    },
    EMERGENCY: {
      sound: 'default',
      priority: 'max',
      vibrationPattern: [0, 1000, 500, 1000, 500, 1000],
    },
  };

  // Determine appropriate notification tone based on type
  getNotificationTone(notificationType) {
    const toneMap = {
      'new_service_request': 'URGENT',
      'service_accepted': 'SUCCESS',
      'service_completed': 'SUCCESS',
      'service_update': 'INFORMATIVE',
      'emergency': 'EMERGENCY',
      'general': 'INFORMATIVE',
    };

    return toneMap[notificationType] || 'INFORMATIVE';
  }

  async scheduleLocalNotification(title, body, data = {}, options = {}) {
    try {
      // Determine tone based on notification type
      const toneType = options.tone || this.getNotificationTone(data.type);
      const tone = FCMService.NOTIFICATION_TONES[toneType] || FCMService.NOTIFICATION_TONES.INFORMATIVE;

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: tone.sound,
          priority: tone.priority,
        },
        trigger: null, // Show immediately
      });

      console.log(`📱 Mechanic: Scheduled ${toneType} notification: "${title}"`);
    } catch (error) {
      console.error('Error scheduling local notification:', error);
    }
  }

  cleanup() {
    // Clean up Expo notification listeners
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }

    // Clean up Firebase messaging listeners
    if (this.unsubscribeOnMessage) {
      this.unsubscribeOnMessage();
      this.unsubscribeOnMessage = null;
    }
    if (this.unsubscribeOnNotificationOpenedApp) {
      this.unsubscribeOnNotificationOpenedApp();
      this.unsubscribeOnNotificationOpenedApp = null;
    }

    // Clean up token refresh interval
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
    }
  }

  // Method to call when user logs in
  async onUserLogin() {
    await this.sendTokenToBackend();
  }

  // Method to call when user logs out
  async onUserLogout() {
    try {
      // Clear local token
      await AsyncStorage.removeItem('fcm_token');
      this.token = null;
    } catch (error) {
      console.error('Error clearing FCM token:', error);
    }
  }
}

export default new FCMService();
