import * as Notifications from 'expo-notifications';
import { Vibration, Alert } from 'react-native';
import { serviceRequestService } from '../api/services';

class ServiceNotificationManager {
  constructor() {
    this.pendingRequests = new Map();
    this.isInitialized = false;
    this.mechanicStatus = 'available'; // available, busy, offline
    this.listeners = new Map(); // Custom event listener system
  }

  // Custom event listener methods
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  emit(event, data) {
    console.log(`🔔 Emitting event: ${event}`, data ? 'with data' : 'without data');

    if (!this.listeners || !this.listeners.has(event)) {
      console.warn(`⚠️ No listeners registered for event: ${event}`);
      return;
    }

    const eventListeners = this.listeners.get(event);
    if (!eventListeners || eventListeners.length === 0) {
      console.warn(`⚠️ No listeners found for event: ${event}`);
      return;
    }

    console.log(`📡 Broadcasting to ${eventListeners.length} listeners for event: ${event}`);

    eventListeners.forEach((callback, index) => {
      try {
        if (typeof callback === 'function') {
          // Add context check for serviceAccepted event to prevent cross-app issues
          if (event === 'serviceAccepted') {
            // Only call if we're in the mechanic app context (has setShowLiveMap function)
            try {
              callback(data);
            } catch (contextError) {
              if (contextError.message && contextError.message.includes('setShowLiveMap')) {
                console.warn(`🚨 ServiceNotificationManager: Skipping ${event} - not in mechanic app context`);
                return;
              }
              throw contextError; // Re-throw if it's a different error
            }
          } else {
            callback(data);
          }
        } else {
          console.error(`❌ Listener ${index} for event ${event} is not a function:`, typeof callback);
        }
      } catch (error) {
        console.error(`❌ Error in event listener ${index} for ${event}:`, error);
        // Don't let one failed listener break others - continue processing
      }
    });
  }

  removeAllListeners() {
    this.listeners.clear();
  }

  initialize() {
    if (this.isInitialized) return;

    this.isInitialized = true;
    console.log('🔔 Service Notification Manager initialized');
  }

  setMechanicStatus(status) {
    this.mechanicStatus = status;
    console.log(`🔧 Mechanic status updated: ${status}`);
  }

  // Validate service request data completeness
  validateServiceRequestData(serviceRequest) {
    const requiredFields = [
      'id',
      'issue_type',
      'issue_description',
      'latitude',
      'longitude',
      'driver_name',
      'truck_info'
    ];

    const missingFields = [];
    const validationErrors = [];

    // Check required fields
    requiredFields.forEach(field => {
      if (!serviceRequest[field] || serviceRequest[field] === '') {
        missingFields.push(field);
      }
    });

    // Validate specific field formats
    if (serviceRequest.latitude && (isNaN(serviceRequest.latitude) || Math.abs(serviceRequest.latitude) > 90)) {
      validationErrors.push('Invalid latitude value');
    }

    if (serviceRequest.longitude && (isNaN(serviceRequest.longitude) || Math.abs(serviceRequest.longitude) > 180)) {
      validationErrors.push('Invalid longitude value');
    }

    // Check for driver information
    if (!serviceRequest.driver_name && !serviceRequest.Driver?.name) {
      missingFields.push('driver_name or Driver.name');
    }

    const isValid = missingFields.length === 0 && validationErrors.length === 0;

    if (!isValid) {
      console.warn('⚠️ Service request validation failed:', {
        missingFields,
        validationErrors,
        serviceRequest: {
          id: serviceRequest.id,
          issue_type: serviceRequest.issue_type,
          hasLatitude: !!serviceRequest.latitude,
          hasLongitude: !!serviceRequest.longitude,
          hasDriverName: !!(serviceRequest.driver_name || serviceRequest.Driver?.name)
        }
      });
    }

    return {
      isValid,
      missingFields,
      validationErrors,
      enhancedRequest: this.enhanceServiceRequestData(serviceRequest)
    };
  }

  // Enhance service request with additional computed data
  enhanceServiceRequestData(serviceRequest) {
    const enhanced = {
      ...serviceRequest,
      // Normalize driver name
      driver_name: serviceRequest.driver_name || serviceRequest.Driver?.name || 'Unknown Driver',
      // Normalize driver phone
      driver_phone: serviceRequest.driver_phone || serviceRequest.Driver?.phone || null,
      // Add urgency if not present
      urgency: serviceRequest.urgency || this.calculateUrgency(serviceRequest.issue_type),
      // Add timestamp if not present
      created_at: serviceRequest.created_at || new Date().toISOString(),
      // Add formatted location
      location_text: this.formatLocationText(serviceRequest.latitude, serviceRequest.longitude),
      // Add estimated distance (will be calculated later)
      distance: null,
      // Add validation status
      validation: {
        isComplete: true,
        validatedAt: new Date().toISOString()
      }
    };

    return enhanced;
  }

  // Calculate urgency based on issue type
  calculateUrgency(issueType) {
    const highUrgencyTypes = ['brake', 'engine', 'accident', 'breakdown'];
    const mediumUrgencyTypes = ['tire', 'battery', 'fuel'];

    if (highUrgencyTypes.includes(issueType?.toLowerCase())) {
      return 'high';
    } else if (mediumUrgencyTypes.includes(issueType?.toLowerCase())) {
      return 'medium';
    }
    return 'normal';
  }

  // Format location for display
  formatLocationText(latitude, longitude) {
    if (!latitude || !longitude) return 'Location unavailable';

    return `${parseFloat(latitude).toFixed(4)}, ${parseFloat(longitude).toFixed(4)}`;
  }

  // Handle incoming service request notification
  handleNewServiceRequest(serviceRequest, skipLocalNotification = false) {
    // Only show notification if mechanic is available
    if (this.mechanicStatus !== 'available') {
      console.log('🔕 Mechanic not available, skipping notification');
      return;
    }

    // Check if this request is already pending
    if (this.pendingRequests.has(serviceRequest.id)) {
      console.log('⚠️ Service request already pending:', serviceRequest.id);
      return;
    }

    console.log('🔔 New service request received:', serviceRequest);

    // Validate and enhance request data
    const validation = this.validateServiceRequestData(serviceRequest);

    if (!validation.isValid) {
      console.error('❌ Service request validation failed:', {
        missingFields: validation.missingFields,
        validationErrors: validation.validationErrors
      });

      // Still show notification but with warning
      Alert.alert(
        'Incomplete Request Data',
        `Service request received but some information is missing: ${validation.missingFields.join(', ')}. Please contact support if this continues.`,
        [{ text: 'OK' }]
      );
    }

    const enhancedRequest = validation.enhancedRequest;

    // Vibrate to alert mechanic
    Vibration.vibrate([0, 500, 200, 500]);

    // Add to pending requests with enhanced data
    this.pendingRequests.set(serviceRequest.id, {
      ...enhancedRequest,
      receivedAt: new Date(),
      timeoutId: null,
      validationStatus: validation.isValid ? 'valid' : 'incomplete'
    });

    // Calculate distance if location is available
    this.calculateDistance(enhancedRequest).then(distance => {
      const finalRequest = {
        ...enhancedRequest,
        distance: distance,
        // Add formatted distance text
        distance_text: distance ? `${distance.toFixed(1)} km away` : 'Distance unknown'
      };

      // Emit event to show dialog with complete data
      this.emit('showServiceDialog', finalRequest);

      // Set auto-reject timeout (60 seconds for detailed review)
      const timeoutId = setTimeout(() => {
        this.handleAutoReject(serviceRequest.id);
      }, 60000);

      // Update timeout ID
      const pending = this.pendingRequests.get(serviceRequest.id);
      if (pending) {
        pending.timeoutId = timeoutId;
        this.pendingRequests.set(serviceRequest.id, pending);
      }
    }).catch(error => {
      console.error('❌ Error calculating distance:', error);

      // Still show dialog even if distance calculation fails
      const finalRequest = {
        ...enhancedRequest,
        distance: null,
        distance_text: 'Distance calculation failed'
      };

      this.emit('showServiceDialog', finalRequest);
    });

    // Show local notification as backup (unless skipped to prevent duplicates)
    if (!skipLocalNotification) {
      this.showLocalNotification(enhancedRequest);
      console.log('📱 Local notification shown for service request:', serviceRequest.id);
    } else {
      console.log('🔕 Skipping local notification to prevent duplicate (FCM already handled)');
    }
  }

  // Calculate distance between mechanic and service location
  async calculateDistance(serviceRequest) {
    try {
      // This would typically use the mechanic's current location and serviceRequest coordinates
      // For now, return a placeholder (serviceRequest contains lat/lng for future use)
      console.log(`Calculating distance for service request ${serviceRequest.id}`);
      return Math.round(Math.random() * 20 + 1); // 1-20 km
    } catch (error) {
      console.error('Error calculating distance:', error);
      return null;
    }
  }

  // Show local notification with appropriate tone
  async showLocalNotification(serviceRequest) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🚛 New Service Request',
          body: `${serviceRequest.issue_type} - ${serviceRequest.issue_description}`,
          data: {
            type: 'new_service_request',
            serviceRequestId: serviceRequest.id,
          },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: null, // Show immediately
      });
      console.log('📱 Mechanic: Showed URGENT service request notification');
    } catch (error) {
      console.error('Error showing local notification:', error);
    }
  }

  // Handle service request acceptance
  async acceptServiceRequest(serviceRequest) {
    try {
      console.log('🔧 acceptServiceRequest called with:', serviceRequest);

      // Validate input
      if (!serviceRequest) {
        throw new Error('Service request is null or undefined');
      }

      if (!serviceRequest.id) {
        console.error('❌ Service request missing ID:', serviceRequest);
        throw new Error('Service request ID is missing');
      }

      console.log('✅ Accepting service request:', serviceRequest.id);

      // Clear timeout
      this.clearRequestTimeout(serviceRequest.id);

      // Call API to accept the request using the extended timeout service
      console.log('📡 Making API call to accept service request with extended timeout');
      console.log('📡 Service request ID:', serviceRequest.id);
      console.log('📡 Estimated arrival:', this.calculateEstimatedArrival());

      const response = await serviceRequestService.acceptServiceRequest(
        serviceRequest.id,
        this.calculateEstimatedArrival()
      );

      console.log('✅ Service request accepted successfully:', response.data);

      // Remove from pending requests
      this.pendingRequests.delete(serviceRequest.id);

      // Update mechanic status
      this.setMechanicStatus('busy');

      // Emit acceptance event
      this.emit('serviceAccepted', {
        serviceRequest,
        assignment: response.data.assignment,
      });

      // Show success notification
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '✅ Service Accepted',
          body: `You've accepted the ${serviceRequest.issue_type} service request`,
          data: {
            type: 'service_accepted',
          },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.DEFAULT,
        },
        trigger: null,
      });
      console.log('📱 Mechanic: Showed SUCCESS service accepted notification');

      return response.data;
    } catch (error) {
      console.error('Error accepting service request:', error);

      // Handle specific error cases
      if (error.response?.status === 409) {
        // Service request already accepted by another mechanic
        this.pendingRequests.delete(serviceRequest.id);

        await Notifications.scheduleNotificationAsync({
          content: {
            title: '⚠️ Request Unavailable',
            body: 'This service request was already accepted by another mechanic',
            sound: 'default',
          },
          trigger: null,
        });

        throw new Error('Service request already accepted by another mechanic');
      }

      throw error;
    }
  }

  // Handle service request rejection
  async rejectServiceRequest(serviceRequest, reason = 'Not available') {
    try {
      console.log('❌ Rejecting service request:', serviceRequest.id);

      // Clear timeout
      this.clearRequestTimeout(serviceRequest.id);

      // For rejection, we just log it locally since there's no assignment to reject yet
      // In a real system, this might notify the backend that the mechanic declined
      console.log(`Mechanic declined service request ${serviceRequest.id}: ${reason}`);

      // Remove from pending requests
      this.pendingRequests.delete(serviceRequest.id);

      // Emit rejection event
      this.emit('serviceRejected', {
        serviceRequest,
        reason,
      });

      console.log('✅ Service request rejected successfully');
    } catch (error) {
      console.error('Error rejecting service request:', error);
      throw error;
    }
  }

  // Handle auto-rejection when timeout expires
  handleAutoReject(serviceRequestId) {
    const pending = this.pendingRequests.get(serviceRequestId);
    if (!pending) return;

    console.log('⏰ Auto-rejecting service request due to timeout:', serviceRequestId);

    // Remove from pending requests
    this.pendingRequests.delete(serviceRequestId);

    // Emit auto-rejection event
    this.emit('serviceAutoRejected', {
      serviceRequest: pending,
      reason: 'No response within time limit',
    });

    // Show alert to user
    Alert.alert(
      'Request Expired',
      'A service request was automatically rejected due to no response.',
      [{ text: 'OK' }]
    );
  }

  // Clear request timeout
  clearRequestTimeout(serviceRequestId) {
    const pending = this.pendingRequests.get(serviceRequestId);
    if (pending && pending.timeoutId) {
      clearTimeout(pending.timeoutId);
      pending.timeoutId = null;
      this.pendingRequests.set(serviceRequestId, pending);
    }
  }

  // Calculate estimated arrival time
  calculateEstimatedArrival() {
    // Simple calculation - in real app, this would use routing APIs
    const now = new Date();
    const estimatedMinutes = Math.round(Math.random() * 30 + 15); // 15-45 minutes
    return new Date(now.getTime() + estimatedMinutes * 60000);
  }

  // Get pending requests count
  getPendingRequestsCount() {
    return this.pendingRequests.size;
  }

  // Get all pending requests
  getPendingRequests() {
    return Array.from(this.pendingRequests.values());
  }

  // Clear all pending requests
  clearAllPendingRequests() {
    this.pendingRequests.forEach((request) => {
      if (request.timeoutId) {
        clearTimeout(request.timeoutId);
      }
    });
    this.pendingRequests.clear();
    console.log('🧹 All pending requests cleared');
  }

  // Handle mechanic going offline
  goOffline() {
    this.setMechanicStatus('offline');
    this.clearAllPendingRequests();
    this.emit('mechanicOffline');
  }

  // Handle mechanic going online
  goOnline() {
    this.setMechanicStatus('available');
    this.emit('mechanicOnline');
  }

  // Cleanup
  destroy() {
    this.clearAllPendingRequests();
    this.removeAllListeners();
    this.isInitialized = false;
    console.log('🗑️ Service Notification Manager destroyed');
  }
}

// Create singleton instance
const serviceNotificationManager = new ServiceNotificationManager();

export default serviceNotificationManager;
