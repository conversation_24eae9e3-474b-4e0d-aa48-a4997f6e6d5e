import React, { useEffect, useRef } from 'react';
import { Platform, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { useDispatch, useSelector } from 'react-redux';
import { updateFCMToken } from '../store/slices/authSlice';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

// Handle job request notifications with Accept/Reject options
const handleJobRequestNotification = (data) => {
  const isUrgent = data.type === 'urgent_job_request';
  const title = isUrgent ? '🚨 URGENT JOB REQUEST' : '🚛 New Job Request';

  Alert.alert(
    title,
    `Driver: ${data.driverName || 'Unknown'}\nLocation: ${data.location || 'Unknown'}\nService: ${data.serviceType || 'General'}\nDistance: ${data.distance || 'Unknown'}\nEstimated Duration: ${data.estimatedDuration || 'Unknown'}`,
    [
      {
        text: '❌ Reject',
        style: 'destructive',
        onPress: () => handleJobResponse(data.requestId, 'rejected')
      },
      {
        text: '✅ Accept',
        style: 'default',
        onPress: () => handleJobResponse(data.requestId, 'accepted')
      }
    ],
    {
      cancelable: false // Force user to make a choice
    }
  );
};

// Handle job response (accept/reject)
const handleJobResponse = async (requestId, response) => {
  try {
    console.log(`Job ${requestId} ${response}`);

    if (response === 'accepted') {
      Alert.alert(
        '✅ Job Accepted!',
        'You have accepted this job. The driver has been notified and you can now proceed to the location.',
        [{ text: 'OK' }]
      );

      // TODO: Send acceptance to backend
      // await api.post(`/mechanics/jobs/${requestId}/accept`);

    } else {
      Alert.alert(
        '❌ Job Rejected',
        'You have rejected this job. It will be offered to other nearby mechanics.',
        [{ text: 'OK' }]
      );

      // TODO: Send rejection to backend
      // await api.post(`/mechanics/jobs/${requestId}/reject`);
    }

    // TODO: Add to job history
    // dispatch(addJobToHistory({ requestId, response, timestamp: new Date() }));

  } catch (error) {
    console.error('Error handling job response:', error);
    Alert.alert('Error', 'Failed to process your response. Please try again.');
  }
};

const NotificationHandler = () => {
  const dispatch = useDispatch();
  const { isAuthenticated } = useSelector(state => state.auth);
  const notificationListener = useRef();
  const responseListener = useRef();

  useEffect(() => {
    if (isAuthenticated) {
      registerForPushNotificationsAsync().then(token => {
        if (token) {
          dispatch(updateFCMToken(token));
        }
      });
    }

    // Note: Notification handling is now centralized in FCM Service
    // This prevents duplicate notifications and ensures consistent behavior
    console.log('NotificationHandler: FCM Service handles all notifications');

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, [isAuthenticated, dispatch]);

  return null; // This component doesn't render anything
};

async function registerForPushNotificationsAsync() {
  let token;

  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      alert('Failed to get push token for push notification!');
      return;
    }
    
    try {
      // Use Firebase FCM instead of Expo tokens
      const messaging = (await import('@react-native-firebase/messaging')).default;

      // Request permission for iOS
      if (Platform.OS === 'ios') {
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (!enabled) {
          console.log('Push notification permission denied');
          return null;
        }
      }

      // Get FCM token
      const fcmToken = await messaging().getToken();
      console.log('Push token:', fcmToken);
      return fcmToken;
    } catch (error) {
      console.error('Error getting push token:', error);
    }
  } else {
    alert('Must use physical device for Push Notifications');
  }

  return token;
}

export default NotificationHandler;
