import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";

// Simple network connectivity check using a quick API call
const checkNetworkConnectivity = async () => {
  try {
    // Make a quick HEAD request to check connectivity
    const response = await fetch("https://www.google.com", {
      method: "HEAD",
      timeout: 5000,
    });
    const isConnected = response.ok;
    console.log(
      "🌐 Network connectivity:",
      isConnected ? "Connected" : "Disconnected"
    );
    return isConnected;
  } catch (error) {
    console.warn("⚠️ Network check failed:", error.message);
    return true; // Assume connected if check fails to avoid blocking requests
  }
};

// Get the local IP address for development
const getLocalIP = () => {
  // For development, we know the server IP
  return "************";
};

// Unified base URL configuration
const BASE_URL = "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com/api";

// Debug logging for network configuration
console.log("🌐 Mechanic API Configuration:");
console.log("🌐 Platform:", Platform.OS);
console.log("🌐 BASE_URL:", BASE_URL);
console.log("🌐 __DEV__:", __DEV__);

// Create axios instance with simple, reliable configuration
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // Increased to 30 seconds for complex operations like service acceptance
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token automatically
api.interceptors.request.use(
  async (config) => {
    try {
      console.log(
        "🌐 API Request:",
        config.method?.toUpperCase(),
        config.baseURL + config.url
      );

      // Skip network connectivity check to avoid delays
      console.log("🌐 Skipping network check to avoid delays");

      const token = await AsyncStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error("Error in request interceptor:", error);
    }
    return config;
  },
  (error) => {
    console.error("❌ Request interceptor error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    console.log("✅ API Response:", response.status, response.config.url);
    return response;
  },
  async (error) => {
    console.error("❌ API Error:", error.message);
    console.error("❌ Error Code:", error.code);
    console.error(
      "❌ Error Response:",
      error.response?.status,
      error.response?.data
    );
    console.error("❌ Error Config:", error.config?.url);

    // Simple error handling without complex retry logic

    // Simple error handling
    if (error.code === "ECONNABORTED") {
      console.error("🕐 Request timeout");
      return Promise.reject(new Error("Request timeout. Please try again."));
    }

    if (error.code === "NETWORK_ERROR" || error.message === "Network Error") {
      console.error("🌐 Network error");
      return Promise.reject(
        new Error("Network error. Please check your connection.")
      );
    }

    if (error.response?.status === 401) {
      // Token expired or invalid, clear storage
      try {
        await AsyncStorage.removeItem("token");
        // Note: Navigation to login should be handled by the calling component
      } catch (storageError) {
        console.error("Error clearing token from AsyncStorage:", storageError);
      }
    }
    return Promise.reject(error);
  }
);

// ===== NOTES =====
// All API methods have been moved to services.js for better organization
// This file now only contains the configured axios instance with interceptors
// Import specific services from './services' instead of using methods from this file

// Test function for network debugging
export const testNetworkConnection = async () => {
  try {
    console.log("🧪 Testing mechanic app network connection...");
    console.log("🧪 Target URL:", BASE_URL);

    const response = await fetch(`${BASE_URL.replace("/api", "")}/api/health`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 10000,
    });

    console.log("🧪 Fetch response status:", response.status);
    const text = await response.text();
    console.log("🧪 Fetch response text:", text);

    return { success: true, status: response.status, data: text };
  } catch (error) {
    console.error("🧪 Mechanic app network test failed:", error);
    return { success: false, error: error.message };
  }
};

export default api;
