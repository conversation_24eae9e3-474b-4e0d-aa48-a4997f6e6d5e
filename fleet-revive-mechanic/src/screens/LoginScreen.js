import React, { useState } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  TextInput,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import CustomText from "../components/CustomText";
import { useTranslation } from "react-i18next";
import { AntDesign, Ionicons } from "@expo/vector-icons";
import ImageBackgroundSuspense from "../components/ImageBackgroundSuspense";
// BlurView removed - no more glass effects
// import { useTheme } from "../context/ThemeContext"; // Temporarily commented out
import KeyboardView from "../components/KeyboardView";
import LanguageSwitcher from "../components/LanguageSwitcher";
import { useDispatch, useSelector } from 'react-redux';
import { loginMechanic, clearError } from '../store/slices/authSlice';
// Temporarily commented out for testing - will re-enable after ATS fix
// import { testNetworkConnection } from '../api/api';
// import { iosNetworkDebug } from '../utils/iosNetworkDebug';
// import { directNetworkTest } from '../utils/directNetworkTest';
// import { emergencyNetworkTest } from '../utils/emergencyNetworkTest';

const imageSource = require("../../assets/images/login-image.jpg");

const LoginScreen = ({ navigation }) => {
  // const { theme } = useTheme(); // Temporarily commented out
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { loading, error } = useSelector(state => state.auth);

  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const handleInputChange = (name, value) => {
    setFormData({ ...formData, [name]: value });
    if (error) {
      dispatch(clearError());
    }
  };

  const handleNetworkDebug = async () => {
    if (!__DEV__) return; // Only available in development

    console.log('🚨 Running emergency network diagnostic...');
    Alert.alert("Emergency Network Debug", "Running emergency connectivity tests... Check console for detailed results.");

    try {
      const results = await emergencyNetworkTest.runAllTests();
      const summary = `Tests: ${results.summary.successful}/${results.summary.total} passed`;

      Alert.alert(
        "Emergency Network Test Results",
        `${summary}\n\nPlatform: ${results.platform}\n\nCheck console for detailed results and troubleshooting info.`
      );
    } catch (error) {
      Alert.alert("Debug Error", `Failed to run emergency diagnostic: ${error.message}`);
    }
  };

  const handleSubmit = async () => {
    console.log('🔘 Login button pressed');
    const { email, password } = formData;
    console.log('📝 Form data:', { email: email?.substring(0, 3) + '***', password: password ? '***' : 'empty' });

    // Basic Validation
    if (!email || !password) {
      console.log('❌ Validation failed: missing email or password');
      Alert.alert("Validation Error", "Please fill in all fields");
      return;
    }

    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(email)) {
      console.log('❌ Validation failed: invalid email format');
      Alert.alert("Validation Error", "Please enter a valid email address");
      return;
    }

    console.log('✅ Validation passed, checking network connectivity...');

    // Quick connectivity check before login - TEMPORARILY DISABLED FOR TESTING
    console.log('🔍 Skipping connectivity check for testing...');
    console.log('⚠️ Note: Connectivity check temporarily disabled to bypass ATS issues');

    // TODO: Re-enable after ATS configuration is working properly
    /*
    try {
      const quickCheck = await directNetworkTest.quickCheck();
      if (!quickCheck.success) {
        console.log('❌ Quick connectivity check failed:', quickCheck.error);

        let alertMessage = `Cannot reach server: ${quickCheck.error}`;

        if (quickCheck.troubleshooting && quickCheck.troubleshooting.length > 0) {
          alertMessage += '\n\nTroubleshooting:\n' + quickCheck.troubleshooting.join('\n• ');
        }

        alertMessage += '\n\nPlease check your network connection and try again.';

        Alert.alert("Connection Error", alertMessage);
        return;
      }
      console.log('✅ Quick connectivity check passed');
    } catch (error) {
      console.log('❌ Connectivity check error:', error.message);
      // Continue anyway - don't block login
    }
    */

    console.log('🚀 Starting login process...');

    try {
      const result = await dispatch(loginMechanic(formData)).unwrap();
      console.log('🎉 Login successful:', result);
      Alert.alert("Login Successful", "Welcome back!");
    } catch (error) {
      console.error("❌ Login error:", error);

      let errorMessage = "Login failed. Please try again.";
      if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert("Login Failed", errorMessage);
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'transparent' }} edges={[]}>
      <KeyboardView>
        <ImageBackgroundSuspense source={imageSource}>
          <View style={styles.container}>
          <View style={styles.backButtonWrapper}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="chevron-back" size={28} color="white" />
            </TouchableOpacity>
            <CustomText bold style={{ fontSize: 25, color: "white" }}>
              {t("mechanic.appName")}
            </CustomText>
          </View>
          <LanguageSwitcher />
          <View style={styles.inputGroup}>
            <TextInput
              style={styles.input}
              placeholder={t("auth.email")}
              onChangeText={(text) => handleInputChange("email", text)}
              value={formData.email}
              keyboardType="email-address"
              returnKeyType="next"
              autoCapitalize="none"
              placeholderTextColor="rgba(0, 0, 0, 0.5)"
              autoCorrect={false}
            />

            <TextInput
              style={styles.input}
              placeholder={t("auth.password")}
              onChangeText={(text) => handleInputChange("password", text)}
              value={formData.password}
              keyboardType="default"
              returnKeyType="done"
              autoCapitalize="none"
              placeholderTextColor="rgba(0, 0, 0, 0.5)"
              autoCorrect={false}
              secureTextEntry={true}
            />

            <TouchableOpacity
              style={styles.button}
              onPress={() => {
                handleSubmit();
              }}
              disabled={loading}
            >
              <CustomText style={styles.buttonText}>
                {loading ? t("common.loading") : t("auth.loginButton")}
              </CustomText>
              <Ionicons
                name="chevron-forward"
                size={24}
                color="white"
                style={styles.buttonIcon}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.googleButton}
              onPress={() => {
                // Call your Google sign-in logic here
                console.log("Google Sign-In Pressed");
              }}
            >
              <AntDesign
                name="google"
                size={24}
                color="white"
                style={styles.googleIcon}
              />
              <CustomText style={styles.buttonText}>
                {t("mechanic.signInWithGoogle")}
              </CustomText>
            </TouchableOpacity>

            <View style={styles.registerView}>
              <CustomText style={{ color: "white", marginEnd: 5 }}>
                {t("mechanic.dontHaveAccount")}
              </CustomText>
              <TouchableOpacity onPress={() => navigation.navigate("Signup")}>
                <CustomText style={{ color: "white" }}>
                  {t("mechanic.register")}
                </CustomText>
              </TouchableOpacity>
            </View>
          </View>

          <CustomText style={styles.forgotPassword}>
            {t("mechanic.forgotPassword")}
          </CustomText>

          {/* Debug button - only visible in development */}
          {__DEV__ && (
            <TouchableOpacity
              style={styles.debugButton}
              onPress={handleNetworkDebug}
            >
              <CustomText style={styles.debugButtonText}>
                🔧 Network Debug
              </CustomText>
            </TouchableOpacity>
          )}
        </View>
        </ImageBackgroundSuspense>
      </KeyboardView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },

  backButtonWrapper: {
    position: "absolute",
    top: 60, // Account for status bar height
    left: 0,
    right: 0,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    zIndex: 1,
  },

  blurView: {
    flex: 1,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },

  backButton: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 24,
    position: "absolute",
    left: 20,
    padding: 10,
  },
  inputGroup: {
    width: "90%",
    position: "absolute",
    bottom: 50,
    marginBottom: 20,
  },
  input: {
    width: "100%",
    fontFamily: "Asap-Regular",
    height: 50,
    paddingHorizontal: 15,
    borderRadius: 15,
    marginBottom: 10,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    color: "black",
    fontSize: 16,
  },
  button: {
    width: "100%",
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 15,
    backgroundColor: "rgba(43, 76, 183, 1)",
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  buttonIcon: {
    position: "absolute",
    right: 15,
  },
  buttonIconText: {
    color: "white",
    fontSize: 16,
  },
  googleButton: {
    width: "100%",
    height: 50,
    borderRadius: 15,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    marginTop: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  googleIcon: {
    marginRight: 10,
  },
  googleButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    height: "100%",
  },
  registerView: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  forgotPassword: {
    color: "white",
    textAlign: "center",
    marginTop: 10,
    fontSize: 14,
    position: "absolute",
    bottom: 30,
  },

  debugButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginTop: 15,
    alignSelf: "center",
  },

  debugButtonText: {
    color: "white",
    fontSize: 14,
    textAlign: "center",
  },
});

export default LoginScreen;
