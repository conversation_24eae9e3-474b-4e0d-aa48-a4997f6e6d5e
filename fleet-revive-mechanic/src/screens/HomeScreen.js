import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  TextInput,
  Image,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { Ionicons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import ActionSheet from "react-native-actions-sheet";
import AsyncStorage from "@react-native-async-storage/async-storage";

import { useTheme } from "../context/ThemeContext";
import {
  fetchServiceRequests,
  hideNotificationDialog,
  completeAssignment,
  startWork,
} from "../store/slices/serviceSlice";
import { setCurrentLocation } from "../store/slices/locationSlice";
import * as Location from "expo-location";

import serviceNotificationManager from "../services/serviceNotificationManager";
import socketService from "../services/socketService";
import fcmService from "../services/fcmService";
import { sharedService } from "../api/services";
import RouteTrackingMap from "../components/RouteTrackingMap";

const HomeScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const insets = useSafeAreaInsets();
  const { user } = useSelector((state) => state.auth);
  const { requests, loading, notificationDialog } = useSelector(
    (state) => state.service
  );
  const { currentLocation } = useSelector((state) => state.location);

  // Error boundary fallback
  if (!theme) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
        <Text>Loading theme...</Text>
      </View>
    );
  }

  const [mechanicStatus, setMechanicStatus] = useState("available"); // available, busy, offline
  const [currentServiceRequest, setCurrentServiceRequest] = useState(null);
  const [activeAssignment, setActiveAssignment] = useState(null);
  const [mechanicLocation, setMechanicLocation] = useState(null);
  const [driverLocation, setDriverLocation] = useState(null);

  // ActionSheet refs
  const serviceRequestActionSheetRef = useRef(null);
  const bottomSheetActionSheetRef = useRef(null);

  // Image state
  const [serviceRequestImages, setServiceRequestImages] = useState([]);
  const [loadingImages, setLoadingImages] = useState(false);

  // Chat state
  const [chatMessage, setChatMessage] = useState("");
  const [chatMessages, setChatMessages] = useState([]);

  // Connection state
  const [socketConnected, setSocketConnected] = useState(false);

  const styles = createStyles(theme, insets);

  useEffect(() => {
    let connectionInterval;

    const initializeApp = async () => {
      try {
        await loadInitialData();
        await requestLocationPermission();
        await initializeNotificationManager();

        // Initialize socket connection
        console.log("🔌 Initializing socket connection...");
        await socketService.connect();

        // Monitor socket connection status
        const checkConnection = () => {
          const isConnected = socketService.isConnected;
          setSocketConnected(isConnected);
          console.log("🔌 Socket connection status:", isConnected);
        };

        // Check connection immediately and then every 5 seconds
        checkConnection();
        connectionInterval = setInterval(checkConnection, 5000);

        // Process any pending initial notifications after component mounts
        setTimeout(() => {
          fcmService.processPendingInitialNotification();
        }, 1000); // Small delay to ensure component is fully mounted
      } catch (error) {
        console.error("❌ Error initializing HomeScreen:", error);
      }
    };

    initializeApp();

    return () => {
      // Cleanup notification manager
      console.log("🧹 Cleaning up HomeScreen...");
      try {
        if (
          serviceNotificationManager &&
          typeof serviceNotificationManager.removeAllListeners === "function"
        ) {
          serviceNotificationManager.removeAllListeners();
          console.log("✅ Service notification manager cleaned up");
        }

        // Clear connection monitoring interval
        if (connectionInterval) {
          clearInterval(connectionInterval);
          console.log("✅ Connection monitoring interval cleared");
        }
      } catch (error) {
        console.error(
          "❌ Error cleaning up service notification manager:",
          error
        );
      }
    };
  }, []);

  // Monitor notification dialog state and show ActionSheet when needed
  useEffect(() => {
    if (notificationDialog.visible && notificationDialog.serviceRequest) {
      // Fetch images for the notification service request
      fetchServiceRequestImages(notificationDialog.serviceRequest.id);
      // Show ActionSheet for FCM notifications
      serviceRequestActionSheetRef.current?.show();
    } else if (!notificationDialog.visible) {
      // If no dialog is currently showing, check for pending notifications
      setTimeout(() => {
        fcmService.processPendingInitialNotification();
      }, 500);
    }
  }, [notificationDialog.visible]);
    // Set up chat message listener
  useEffect(() => {
    const handleNewMessage = (data) => {
      console.log("💬 New message received:", data);
      if (
        data.senderType === "driver" &&
        activeAssignment &&
        data.serviceRequestId === activeAssignment.service_request.id
      ) {
        const newMessage = {
          id: Date.now().toString(),
          text: data.message,
          sender: "driver",
          senderName: data.senderName || "Driver",
          timestamp: new Date(data.timestamp),
          type: data.messageType || "text",
        };
        setChatMessages((prev) => [...prev, newMessage]);
        console.log("✅ Added driver message to chat");
      }
    };

    if (socketService.isConnected) {
      socketService.addEventListener("new_message", handleNewMessage);
    }

    return () => {
      socketService.removeEventListener("new_message", handleNewMessage);
    };
  }, [activeAssignment]);

  // Initialize service notification manager
  const initializeNotificationManager = async () => {
    console.log("🔧 Initializing service notification manager...");

    try {
      // Initialize FCM service first
      console.log("🔔 Initializing FCM service...");
      await fcmService.initialize();
      console.log("✅ FCM service initialized");

      // Initialize service notification manager
      serviceNotificationManager.initialize();
      serviceNotificationManager.setMechanicStatus(mechanicStatus);

      // Listen for new service requests
      console.log("🔧 Setting up event listeners...");
      serviceNotificationManager.on(
        "showServiceDialog",
        handleShowServiceDialog
      );
      serviceNotificationManager.on("serviceAccepted", handleServiceAccepted);
      serviceNotificationManager.on("serviceRejected", handleServiceRejected);
      serviceNotificationManager.on(
        "serviceAutoRejected",
        handleServiceAutoRejected
      );

      console.log("✅ Service notification manager initialized successfully");
    } catch (error) {
      console.error(
        "❌ Error initializing service notification manager:",
        error
      );
    }

    // Listen for socket events
    socketService.addEventListener(
      "service_request_unavailable",
      handleServiceUnavailable
    );
    socketService.addEventListener(
      "assignment_confirmed",
      handleAssignmentConfirmed
    );
    socketService.addEventListener(
      "driver_location_update",
      handleDriverLocationUpdate
    );
  };

  // Fetch service request images
  const fetchServiceRequestImages = async (serviceRequestId) => {
    if (!serviceRequestId) return;

    try {
      setLoadingImages(true);
      console.log("🖼️ Fetching images for service request:", serviceRequestId);

      const response = await sharedService.getServiceRequestFiles(
        serviceRequestId
      );
      const images = response.data.files || [];

      console.log("🖼️ Fetched images:", images.length, "images");
      setServiceRequestImages(images);
    } catch (error) {
      console.error("❌ Error fetching service request images:", error);
      setServiceRequestImages([]);
    } finally {
      setLoadingImages(false);
    }
  };

  // Send chat message
  const sendChatMessage = async () => {
    if (!chatMessage.trim() || !activeAssignment) return;

    const serviceRequestId = activeAssignment.service_request.id;
    const messageText = chatMessage.trim();
    console.log(
      "💬 Sending chat message:",
      messageText,
      "to service:",
      serviceRequestId
    );

    // Add message to local state immediately for optimistic UI
    const newMessage = {
      id: Date.now().toString(),
      text: messageText,
      sender: "mechanic",
      senderName: "You",
      timestamp: new Date(),
      type: "text",
    };

    setChatMessages((prev) => [...prev, newMessage]);

    // Clear input immediately for better UX
    setChatMessage("");

    try {
      // 1. Send via Socket.IO for real-time delivery
      if (socketService.isConnected) {
        socketService.sendChatMessage(serviceRequestId, messageText, "text");
        console.log("✅ Message sent via Socket.IO");
      } else {
        console.warn(
          "⚠️ Socket.IO not connected, message may not be delivered in real-time"
        );
      }

      // 2. Send via API for persistence
      const response = await fetch(
        `${
          process.env.EXPO_PUBLIC_API_URL || "http://192.168.2.17:5001/api"
        }/chat/send`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${
              user?.token || (await AsyncStorage.getItem("token"))
            }`,
          },
          body: JSON.stringify({
            service_request_id: serviceRequestId,
            message: messageText,
            message_type: "text",
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log("✅ Message saved to database:", result);
    } catch (error) {
      console.error("❌ Error sending message:", error);

      // Show error to user but keep the message in local state
      Alert.alert(
        "Message Error",
        "Failed to send message. Please check your connection and try again.",
        [
          {
            text: "Retry",
            onPress: () => {
              // Re-add the message text to input for retry
              setChatMessage(messageText);
              // Remove the failed message from local state
              setChatMessages((prev) =>
                prev.filter((msg) => msg.id !== newMessage.id)
              );
            },
          },
          {
            text: "Cancel",
            style: "cancel",
          },
        ]
      );
    }
  };

  // Handle showing service dialog
  const handleShowServiceDialog = (serviceRequest) => {
    try {
      console.log("🔔 Showing service dialog for request:", serviceRequest?.id);
      console.log("🔔 Full service request data:", serviceRequest);

      if (!serviceRequest) {
        console.error(
          "❌ No service request provided to handleShowServiceDialog"
        );
        return;
      }

      if (!serviceRequest.id) {
        console.error("❌ Service request missing ID:", serviceRequest);
        Alert.alert('Error', 'Service request is missing ID. Please try again.');
        return;
      }

      console.log("✅ Setting currentServiceRequest with ID:", serviceRequest.id);
      setCurrentServiceRequest(serviceRequest);

      // Fetch images for this service request
      fetchServiceRequestImages(serviceRequest.id);
      serviceRequestActionSheetRef.current?.show();
    } catch (error) {
      console.error("❌ Error in handleShowServiceDialog:", error);
    }
  };

  // Handle service acceptance
  const handleServiceAccepted = ({ serviceRequest, assignment }) => {
    console.log("✅ Service accepted:", serviceRequest.id);
    setMechanicStatus("busy");
    serviceNotificationManager.setMechanicStatus("busy");

    // Set active assignment and show live tracking
    setActiveAssignment(assignment);
    setShowLiveMap(true);

    // Set initial driver location
    if (serviceRequest.latitude && serviceRequest.longitude) {
      setDriverLocation({
        latitude: parseFloat(serviceRequest.latitude),
        longitude: parseFloat(serviceRequest.longitude),
      });
    }

    // Join service room for real-time chat
    if (socketService.isConnected) {
      socketService.joinServiceRoom(serviceRequest.id);
      console.log("🏠 Joined service room for chat:", serviceRequest.id);
    }

    // Navigate to service details or tracking screen
    navigation.navigate("Services", {
      screen: "ServiceDetails",
      params: { requestId: serviceRequest.id },
    });
  };

  // Handle service rejection
  const handleServiceRejected = ({ serviceRequest, reason }) => {
    console.log("❌ Service rejected:", serviceRequest.id, reason);
  };

  // Handle auto-rejection
  const handleServiceAutoRejected = ({ serviceRequest, reason }) => {
    console.log("⏰ Service auto-rejected:", serviceRequest.id, reason);
  };

  // Handle service completion from HomeScreen
  const handleCompleteService = async () => {
    if (!activeAssignment) return;

    Alert.alert(
      "Complete Service",
      "Are you sure you want to mark this service as completed?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Complete",
          style: "default",
          onPress: async () => {
            try {
              console.log("🏁 Starting service completion process...");
              console.log("🏁 Active assignment ID:", activeAssignment?.id);
              console.log(
                "🏁 Active assignment data:",
                JSON.stringify(activeAssignment, null, 2)
              );
              console.log("🏁 Assignment status:", activeAssignment?.status);
              console.log(
                "🏁 Service request ID:",
                activeAssignment?.service_request?.id
              );

              if (!activeAssignment || !activeAssignment.id) {
                throw new Error("No active assignment found");
              }

              // Check if assignment needs to be started first
              if (activeAssignment.status === "accepted") {
                console.log(
                  "🔧 Assignment is in accepted status, starting work first..."
                );
                try {
                  const startResult = await dispatch(
                    startWork(activeAssignment.id)
                  ).unwrap();
                  console.log("✅ Work started successfully:", startResult);
                } catch (startError) {
                  console.error("❌ Failed to start work:", startError);
                  throw new Error(
                    `Failed to start work: ${startError.message}`
                  );
                }
              }

              // Complete the assignment (this will also update the service request status)
              console.log("🏁 Dispatching completeAssignment action...");
              const completionResult = await dispatch(
                completeAssignment({
                  assignmentId: activeAssignment.id,
                  notes: "Service completed successfully from home screen",
                })
              ).unwrap();

              console.log(
                "✅ Assignment completed successfully:",
                completionResult
              );

              // Send completion notification to driver via Socket.IO
              if (socketService.isConnected) {
                socketService.notifyServiceCompleted(
                  activeAssignment.service_request.id
                );
                // Leave service room
                socketService.leaveServiceRoom(
                  activeAssignment.service_request.id
                );
              }

              // Reset states
              setActiveAssignment(null);
              setShowLiveMap(false);
              setMechanicStatus("available");
              serviceNotificationManager.setMechanicStatus("available");

              // Clear chat messages
              setChatMessages([]);
              setChatMessage("");

              // Refresh service requests
              dispatch(fetchServiceRequests());

              Alert.alert(
                "Service Completed! 🎉",
                "The service has been marked as completed and the driver has been notified."
              );
            } catch (error) {
              console.error("Error completing service:", error);
              console.error("Error details:", {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status,
              });

              Alert.alert(
                "Error",
                error.message ||
                  error.response?.data?.message ||
                  "Failed to complete service. Please try again."
              );
            }
          },
        },
      ]
    );
  };

  // Handle dialog accept action
  const handleDialogAccept = async (serviceRequest) => {
    try {
      console.log('🔧 handleDialogAccept called with:', serviceRequest);

      // Validate service request data
      if (!serviceRequest) {
        console.error('❌ Service request is null/undefined');
        Alert.alert('Error', 'Service request data is missing. Please try again.');
        return;
      }

      if (!serviceRequest.id) {
        console.error('❌ Service request ID is missing:', serviceRequest);
        Alert.alert('Error', 'Service request ID is missing. Please try again.');
        return;
      }

      console.log('✅ Accepting service request:', serviceRequest.id);
      await serviceNotificationManager.acceptServiceRequest(serviceRequest);

      dispatch(hideNotificationDialog());
      serviceRequestActionSheetRef.current?.hide();
      setCurrentServiceRequest(null);

      // Show success message
      Alert.alert('Success', 'Service request accepted successfully!');

      // Refresh service requests list to update the status
      dispatch(fetchServiceRequests());
    } catch (error) {
      console.error("❌ Error accepting service request:", error);
      Alert.alert(
        'Error',
        error.message || 'Failed to accept service request. Please try again.'
      );
    }
  };

  // Handle dialog reject action
  const handleDialogReject = async (serviceRequest) => {
    try {
      // First reject locally through notification manager
      await serviceNotificationManager.rejectServiceRequest(
        serviceRequest,
        "Mechanic declined"
      );

      // Then update backend to mark as rejected (optional - for tracking purposes)
      try {
        // Call backend to log the rejection (this doesn't change the request status to keep it available for other mechanics)
        await api.post("/assignments/log-rejection", {
          service_request_id: serviceRequest.id,
          rejection_reason: "Mechanic declined",
        });
        console.log("✅ Rejection logged to backend");
      } catch (backendError) {
        console.log(
          "⚠️ Failed to log rejection to backend (non-critical):",
          backendError.message
        );
        // Don't throw - rejection should still work locally
      }

      dispatch(hideNotificationDialog());
      serviceRequestActionSheetRef.current?.hide();
      setCurrentServiceRequest(null);

      // Refresh service requests list to update the UI
      dispatch(fetchServiceRequests());
    } catch (error) {
      console.error("Error rejecting service request:", error);
      throw error;
    }
  };

  // Handle service request becoming unavailable
  const handleServiceUnavailable = (data) => {
    const { service_request_id, accepted_by } = data;

    // Remove from current dialog if showing
    if (
      currentServiceRequest?.id === service_request_id ||
      notificationDialog.serviceRequest?.id === service_request_id
    ) {
      dispatch(hideNotificationDialog());
      serviceRequestActionSheetRef.current?.hide();
      setCurrentServiceRequest(null);
    }

    console.log(
      `🚫 Service request ${service_request_id} accepted by ${accepted_by}`
    );
  };

  // Handle assignment confirmation
  const handleAssignmentConfirmed = (data) => {
    const { assignment, service_request } = data;

    setActiveAssignment(assignment);
    setShowLiveMap(true);
    setMechanicStatus("busy");

    // Set driver location
    if (service_request.latitude && service_request.longitude) {
      setDriverLocation({
        latitude: parseFloat(service_request.latitude),
        longitude: parseFloat(service_request.longitude),
      });
    }

    console.log(`✅ Assignment confirmed for service ${service_request.id}`);
  };

  // Handle driver location updates
  const handleDriverLocationUpdate = (data) => {
    if (
      activeAssignment &&
      data.serviceRequestId === activeAssignment.service_request_id
    ) {
      setDriverLocation({
        latitude: data.latitude,
        longitude: data.longitude,
        heading: data.heading,
        speed: data.speed,
      });
    }
  };

  // Handle mechanic location updates
  const handleMechanicLocationUpdate = (location) => {
    setMechanicLocation(location);

    // Send location update via socket if we have an active assignment
    if (activeAssignment) {
      socketService.updateMechanicLocation(location);
    }
  };

  // Production: Test functions removed for production build

  const loadInitialData = async () => {
    try {
      await dispatch(fetchServiceRequests()).unwrap();
    } catch (error) {
      console.error("Error loading initial data:", error);
    }
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === "granted") {
        let locationData;

        try {
          const location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.High,
            timeout: 10000,
          });

          locationData = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };
        } catch (locationError) {
          console.warn(
            "⚠️ Could not get current location, using default location for development"
          );
          // Default location (Toronto, Canada) for development/simulator
          locationData = {
            latitude: 43.6532,
            longitude: -79.3832,
          };
        }

        // Set both Redux state and local state
        dispatch(setCurrentLocation(locationData));
        setMechanicLocation(locationData);

        console.log("📍 Mechanic location set:", locationData);
      } else {
        console.error("❌ Location permission denied");
        // Use default location even without permission for development
        const defaultLocation = {
          latitude: 43.6532,
          longitude: -79.3832,
        };
        dispatch(setCurrentLocation(defaultLocation));
        setMechanicLocation(defaultLocation);
        console.log("📍 Using default location:", defaultLocation);

        Alert.alert(
          "Permission Required",
          "Location permission is required for the app to work properly. Using default location for now."
        );
      }
    } catch (error) {
      console.error("❌ Error getting location:", error);
      // Fallback to default location
      const defaultLocation = {
        latitude: 43.6532,
        longitude: -79.3832,
      };
      dispatch(setCurrentLocation(defaultLocation));
      setMechanicLocation(defaultLocation);
      console.log("📍 Using fallback location:", defaultLocation);
    }
  };

  // Production: Refresh functionality integrated into main data loading

  const toggleStatus = () => {
    const statusOptions = ["available", "busy", "offline"];
    const currentIndex = statusOptions.indexOf(mechanicStatus);
    const nextIndex = (currentIndex + 1) % statusOptions.length;
    const newStatus = statusOptions[nextIndex];

    setMechanicStatus(newStatus);
    serviceNotificationManager.setMechanicStatus(newStatus);

    // Handle status-specific actions
    if (newStatus === "offline") {
      serviceNotificationManager.goOffline();
    } else if (newStatus === "available" && mechanicStatus === "offline") {
      serviceNotificationManager.goOnline();
    }
  };

  const getStatusColor = () => {
    switch (mechanicStatus) {
      case "available":
        return theme.colors.success;
      case "busy":
        return theme.colors.warning;
      case "offline":
        return theme.colors.textTertiary;
      default:
        return theme.colors.textTertiary;
    }
  };

  const getStatusText = () => {
    switch (mechanicStatus) {
      case "available":
        return "Available";
      case "busy":
        return "Busy";
      case "offline":
        return "Offline";
      default:
        return "Unknown";
    }
  };

  // Use Redux location as fallback if local state is not set
  const effectiveMechanicLocation = mechanicLocation || currentLocation;

  // Debug logging for white screen issue
  if (__DEV__) {
    console.log("🔧 Mechanic App: Render state check:", {
      hasLocation: !!effectiveMechanicLocation,
      requestsCount: requests.length,
      loading: loading,
      currentLocation: currentLocation,
      mechanicLocation: mechanicLocation,
    });
  }

  // Show loading if location is not available AND we haven't tried to load data yet
  if (!effectiveMechanicLocation && requests.length === 0 && !loading) {
    console.log("🔧 Mechanic App: Showing location loading screen");
    return (
      <View style={styles.centeredContainer}>
        <View style={styles.loadingCard}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Getting your location...</Text>
          <Text style={styles.loadingSubtext}>
            Please ensure GPS is enabled
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              // Retry location and data fetch
              requestLocationPermission();
              dispatch(fetchServiceRequests());
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Show error state if we have network issues but no location
  if (!effectiveMechanicLocation && requests.length === 0) {
    return (
      <View style={styles.centeredContainer}>
        <View style={styles.errorCard}>
          <Ionicons
            name="location-outline"
            size={48}
            color={theme.colors.textTertiary}
          />
          <Text style={styles.errorText}>Unable to get location</Text>
          <Text style={styles.errorSubtext}>
            Please check your location permissions and network connection
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              requestLocationPermission();
              dispatch(fetchServiceRequests());
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (__DEV__) {
    console.log("🔧 Mechanic App: Rendering main UI with:", {
      activeAssignment: !!activeAssignment,
      effectiveMechanicLocation: !!effectiveMechanicLocation,
      driverLocation: !!driverLocation,
    });
  }

  return (
    <View style={styles.container}>
      {/* Full Screen Map */}
      {effectiveMechanicLocation ? (
        <RouteTrackingMap
          serviceRequest={activeAssignment?.service_request}
          mechanicLocation={effectiveMechanicLocation}
          driverLocation={driverLocation}
          onLocationUpdate={handleMechanicLocationUpdate}
          style={styles.fullScreenMap}
        />
      ) : (
        <View style={[styles.fullScreenMap, { backgroundColor: theme.colors.background, justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={{ color: theme.colors.textPrimary, marginTop: 16 }}>
            Loading map...
          </Text>
        </View>
      )}

      {/* Status Toggle - Top Right */}
      <TouchableOpacity style={styles.statusContainer} onPress={toggleStatus}>
        <View
          style={[styles.statusDot, { backgroundColor: getStatusColor() }]}
        />
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {getStatusText()}
        </Text>
      </TouchableOpacity>

      {/* Connection Status - Top Right Below Status */}
      <View style={styles.connectionContainer}>
        <View
          style={[
            styles.connectionDot,
            { backgroundColor: socketConnected ? "#4CAF50" : "#F44336" },
          ]}
        />
        <Text style={styles.connectionText}>
          {socketConnected ? "Connected" : "Disconnected"}
        </Text>
      </View>

      {/* Debug Panel - Top Left */}
      <TouchableOpacity
        style={styles.debugPanel}
        onPress={() => {
          console.log("🔍 DEBUG INFO:");
          console.log("- Mechanic Status:", mechanicStatus);
          console.log("- Socket Connected:", socketConnected);
          console.log("- FCM Initialized:", fcmService.isInitialized);
          console.log("- Active Assignment:", !!activeAssignment);
          console.log("- User:", user?.name);
        }}
      >
        <Text style={styles.debugText}>DEBUG</Text>
      </TouchableOpacity>

      {/* Test Notification Button - Top Left Below Debug */}
      {/* <TouchableOpacity style={styles.testButton} onPress={testNotifications}>
        <Text style={styles.testButtonText}>TEST 🔔</Text>
      </TouchableOpacity> */}

      {/* Test Button (Development Only) */}
      {/* {__DEV__ && (
          <TouchableOpacity
            style={styles.testButton}
            onPress={simulateNewServiceRequest}
          >
            <Ionicons name="notifications" size={16} color={theme.colors.primary} />
          </TouchableOpacity>
        )} */}

      {/* ActionSheet Trigger for Active Assignment */}
      {activeAssignment && driverLocation && (
        <TouchableOpacity
          style={styles.actionSheetTrigger}
          onPress={() => bottomSheetActionSheetRef.current?.show()}
        >
          <Ionicons name="chevron-up" size={24} color={theme.colors.primary} />
          <Text style={styles.actionSheetTriggerText}>Chat & Actions</Text>
        </TouchableOpacity>
      )}

      {/* Service Request ActionSheet - Non-dismissible */}
      <ActionSheet
        ref={serviceRequestActionSheetRef}
        containerStyle={styles.actionSheetContainer}
        gestureEnabled={false}
        headerAlwaysVisible={true}
        closable={false}
        closeOnTouchBackdrop={false}
        CustomHeaderComponent={
          <View style={styles.actionSheetHeader}>
            <View style={styles.actionSheetHeaderContent}>
              <Ionicons
                name="alert-circle"
                size={24}
                color={theme.colors.warning}
              />
              <Text style={styles.actionSheetTitle}>New Service Request</Text>
              <View style={styles.urgencyIndicator}>
                <Text style={styles.urgencyText}>
                  {currentServiceRequest?.urgency?.toUpperCase() || "NORMAL"}
                </Text>
              </View>
            </View>
            <Text style={styles.actionSheetSubtitle}>
              Please accept or reject this request
            </Text>
          </View>
        }
      >
        <View style={styles.serviceRequestContent}>
          {(currentServiceRequest || notificationDialog.serviceRequest) && (
            <>
              {/* Request ID and Validation Status */}
              <View style={styles.requestInfo}>
                <Text style={styles.requestIdLabel}>Request ID</Text>
                <Text style={styles.requestIdText}>
                  #
                  {(
                    currentServiceRequest || notificationDialog.serviceRequest
                  )?.id
                    ?.toString()
                    .slice(-6) || "N/A"}
                </Text>
                {(currentServiceRequest || notificationDialog.serviceRequest)
                  ?.validation &&
                  !(currentServiceRequest || notificationDialog.serviceRequest)
                    ?.validation?.isComplete && (
                    <Text style={styles.validationWarning}>
                      ⚠️ Some information may be incomplete
                    </Text>
                  )}
              </View>

              <View style={styles.requestInfo}>
                <Text style={styles.driverLabel}>Driver Information</Text>
                <Text style={styles.driverName}>
                  {(currentServiceRequest || notificationDialog.serviceRequest)
                    ?.driver_name ||
                    (currentServiceRequest || notificationDialog.serviceRequest)
                      ?.Driver?.name ||
                    "Unknown Driver"}
                </Text>
                {((currentServiceRequest || notificationDialog.serviceRequest)
                  ?.driver_phone ||
                  (currentServiceRequest || notificationDialog.serviceRequest)
                    ?.Driver?.phone) && (
                  <Text style={styles.driverPhone}>
                    📞{" "}
                    {(
                      currentServiceRequest || notificationDialog.serviceRequest
                    )?.driver_phone ||
                      (
                        currentServiceRequest ||
                        notificationDialog.serviceRequest
                      )?.Driver?.phone}
                  </Text>
                )}
              </View>

              <View style={styles.requestInfo}>
                <Text style={styles.locationLabel}>Location</Text>
                <Text style={styles.locationText}>
                  {
                    (currentServiceRequest || notificationDialog.serviceRequest)
                      ?.latitude
                  }
                  ,{" "}
                  {
                    (currentServiceRequest || notificationDialog.serviceRequest)
                      ?.longitude
                  }
                </Text>
              </View>

              <View style={styles.requestInfo}>
                <Text style={styles.locationLabel}>Truck Info</Text>
                <Text style={styles.locationText}>
                  {(currentServiceRequest || notificationDialog.serviceRequest)
                    ?.truck_info || "Truck information not available"}
                </Text>
              </View>

              <View style={styles.requestInfo}>
                <Text style={styles.issueLabel}>Issue</Text>
                <Text style={styles.issueText}>
                  {(() => {
                    const request =
                      currentServiceRequest ||
                      notificationDialog.serviceRequest;
                    console.log(
                      "🔍 Dialog Debug - Full request object:",
                      request
                    );
                    console.log(
                      "🔍 Dialog Debug - issue_description:",
                      request?.issue_description
                    );
                    console.log(
                      "🔍 Dialog Debug - issue_type:",
                      request?.issue_type
                    );

                    return request?.issue_description &&
                      request.issue_description !== "Service request"
                      ? request.issue_description
                      : `${request?.issue_type || "Service"} request`;
                  })()}
                </Text>
              </View>

              {/* Service Request Images */}
              {loadingImages ? (
                <View style={styles.imagesSection}>
                  <Text style={styles.imagesSectionTitle}>Images</Text>
                  <View style={styles.imagesLoadingContainer}>
                    <ActivityIndicator
                      size="small"
                      color={theme.colors.primary}
                    />
                    <Text style={styles.imagesLoadingText}>
                      Loading images...
                    </Text>
                  </View>
                </View>
              ) : serviceRequestImages.length > 0 ? (
                <View style={styles.imagesSection}>
                  <Text style={styles.imagesSectionTitle}>
                    Images ({serviceRequestImages.length})
                  </Text>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.imagesScrollView}
                  >
                    {serviceRequestImages.map((image, index) => (
                      <TouchableOpacity
                        key={image.id || index}
                        style={styles.imageContainer}
                        onPress={() => {
                          // TODO: Add full-screen image viewer
                          console.log("🖼️ Image tapped:", image.file_url);
                        }}
                      >
                        <Image
                          source={{ uri: image.file_url }}
                          style={styles.serviceImage}
                          resizeMode="cover"
                        />
                        <View style={styles.imageOverlay}>
                          <Ionicons name="expand" size={16} color="white" />
                        </View>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              ) : null}

              <View style={styles.dialogActions}>
                <TouchableOpacity
                  style={styles.rejectButton}
                  onPress={() =>
                    handleDialogReject(
                      currentServiceRequest || notificationDialog.serviceRequest
                    )
                  }
                >
                  <Text style={styles.rejectButtonText}>Reject</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.acceptButton}
                  onPress={() => {
                    const requestToAccept = currentServiceRequest || notificationDialog.serviceRequest;
                    console.log('🔧 Accept button pressed - currentServiceRequest:', currentServiceRequest);
                    console.log('🔧 Accept button pressed - notificationDialog.serviceRequest:', notificationDialog.serviceRequest);
                    console.log('🔧 Accept button pressed - final request:', requestToAccept);
                    handleDialogAccept(requestToAccept);
                  }}
                >
                  <Text style={styles.acceptButtonText}>Accept Request</Text>
                </TouchableOpacity>
              </View>

              {/* Close Button with Confirmation */}
              <TouchableOpacity
                style={styles.closeDialogButton}
                onPress={() => {
                  Alert.alert(
                    "Close Request Dialog",
                    "Are you sure you want to close this dialog? The request will be automatically rejected.",
                    [
                      {
                        text: "Cancel",
                        style: "cancel",
                      },
                      {
                        text: "Close & Reject",
                        style: "destructive",
                        onPress: () => {
                          handleDialogReject(
                            currentServiceRequest ||
                              notificationDialog.serviceRequest
                          );
                        },
                      },
                    ]
                  );
                }}
              >
                <Ionicons
                  name="close-circle"
                  size={20}
                  color={theme.colors.textSecondary}
                />
                <Text style={styles.closeDialogText}>Close Dialog</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </ActionSheet>

      {/* Bottom Sheet ActionSheet for Chat & Actions */}
      <ActionSheet
        ref={bottomSheetActionSheetRef}
        containerStyle={styles.actionSheetContainer}
        gestureEnabled={true}
        headerAlwaysVisible={true}
        CustomHeaderComponent={
          <View style={styles.actionSheetHeader}>
            <Text style={styles.actionSheetTitle}>
              Chat with{" "}
              {activeAssignment?.service_request?.Driver?.name ||
                activeAssignment?.service_request?.driver_name ||
                "Driver"}
            </Text>
            <TouchableOpacity
              onPress={() => bottomSheetActionSheetRef.current?.hide()}
              style={styles.actionSheetCloseButton}
            >
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
        }
      >
        <View style={styles.bottomSheetContent}>
          {activeAssignment && (
            <>
              {/* Driver Information Section */}
              <View style={styles.driverInfoSection}>
                <View style={styles.driverHeader}>
                  <View style={styles.driverAvatar}>
                    <Ionicons
                      name="person"
                      size={24}
                      color={theme.colors.primary}
                    />
                  </View>
                  <View style={styles.driverDetails}>
                    <Text style={styles.driverNameText}>
                      {activeAssignment?.service_request?.Driver?.name ||
                        activeAssignment?.service_request?.driver_name ||
                        "Driver"}
                    </Text>
                    <Text style={styles.driverPhoneText}>
                      {activeAssignment.service_request?.Driver?.phone ||
                        "No phone available"}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.infoButton}
                    onPress={() => {
                      bottomSheetActionSheetRef.current?.hide();
                      navigation.navigate("Services", {
                        screen: "ServiceDetails",
                        params: {
                          requestId: activeAssignment.service_request.id,
                        },
                      });
                    }}
                  >
                    <Ionicons
                      name="information-circle"
                      size={20}
                      color={theme.colors.primary}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Chat Interface */}
              <View style={styles.chatContainer}>
                <View style={styles.chatMessages}>
                  {chatMessages.length > 0 ? (
                    <ScrollView style={styles.messagesScrollView}>
                      {chatMessages.map((message, index) => (
                        <View
                          key={message.id || index}
                          style={[
                            styles.messageContainer,
                            message.sender === "mechanic"
                              ? styles.mechanicMessage
                              : styles.driverMessage,
                          ]}
                        >
                          <Text
                            style={[
                              styles.messageText,
                              message.sender === "mechanic"
                                ? styles.mechanicMessageText
                                : styles.driverMessageText,
                            ]}
                          >
                            {message.text}
                          </Text>
                          <Text style={styles.messageTime}>
                            {new Date(message.timestamp).toLocaleTimeString(
                              [],
                              {
                                hour: "2-digit",
                                minute: "2-digit",
                              }
                            )}
                          </Text>
                        </View>
                      ))}
                    </ScrollView>
                  ) : (
                    <View style={styles.chatWelcomeMessage}>
                      <Ionicons
                        name="chatbubble-outline"
                        size={24}
                        color={theme.colors.textSecondary}
                      />
                      <Text style={styles.chatWelcomeText}>
                        Start a conversation with the driver
                      </Text>
                      <Text style={styles.chatWelcomeSubtext}>
                        Messages will appear here once you start chatting
                      </Text>
                    </View>
                  )}
                </View>
                <View style={styles.chatInputContainer}>
                  <TextInput
                    style={styles.chatInput}
                    placeholder="Type your message..."
                    placeholderTextColor={theme.colors.textSecondary}
                    value={chatMessage}
                    onChangeText={setChatMessage}
                    multiline
                    maxLength={500}
                  />
                  <TouchableOpacity
                    style={[
                      styles.sendButton,
                      !chatMessage.trim() && styles.sendButtonDisabled,
                    ]}
                    onPress={sendChatMessage}
                    disabled={!chatMessage.trim()}
                  >
                    <Ionicons
                      name="send"
                      size={18}
                      color={
                        chatMessage.trim()
                          ? theme.colors.surface
                          : theme.colors.textSecondary
                      }
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Finish Service Button */}
              <TouchableOpacity
                style={styles.finishServiceButton}
                onPress={() => {
                  bottomSheetActionSheetRef.current?.hide();
                  handleCompleteService();
                }}
              >
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={theme.colors.surface}
                />
                <Text style={styles.finishServiceText}>Finish Service</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </ActionSheet>
    </View>
  );
};

const createStyles = (theme, insets = { top: 0, bottom: 0 }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    centeredContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: theme.colors.background,
    },
    loadingCard: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.xl,
      borderRadius: theme.borderRadius.xl,
      alignItems: "center",
      marginHorizontal: theme.spacing.lg,
      ...theme.shadows.lg,
    },
    loadingText: {
      marginTop: theme.spacing.md,
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.text,
      textAlign: "center",
    },
    loadingSubtext: {
      marginTop: theme.spacing.sm,
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: "center",
    },
    errorCard: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.xl,
      borderRadius: theme.borderRadius.xl,
      alignItems: "center",
      marginHorizontal: theme.spacing.lg,
      ...theme.shadows.lg,
    },
    errorText: {
      marginTop: theme.spacing.md,
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.text,
      textAlign: "center",
    },
    errorSubtext: {
      marginTop: theme.spacing.sm,
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: "center",
      paddingHorizontal: theme.spacing.lg,
    },
    retryButton: {
      marginTop: theme.spacing.lg,
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.xl,
      paddingVertical: theme.spacing.md,
      borderRadius: theme.borderRadius.xl,
      ...theme.shadows.sm,
    },
    retryButtonText: {
      color: theme.colors.white,
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.bold,
      textAlign: "center",
    },
    fullScreenMap: {
      flex: 1,
    },
    statusContainer: {
      position: "absolute",
      top: insets.top + 10, // Use safe area insets + 10px padding
      left: theme.spacing.md,
      flexDirection: "row",
      alignItems: "center",
      zIndex: 10,
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderRadius: theme.borderRadius.lg,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      ...theme.shadows.md,
    },
    connectionIndicator: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.xs,
    },
    connectedDot: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: theme.colors.success,
      marginRight: theme.spacing.sm,
    },
    availableDot: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: theme.colors.primary,
      marginRight: theme.spacing.sm,
    },
    connectionText: {
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.text,
    },
    driverName: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    availableText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
    },

    scrollView: {
      flex: 1,
    },
    contentContainer: {
      paddingBottom: theme.spacing.xl,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: theme.spacing.lg,
      paddingTop: theme.spacing.lg,
      paddingBottom: theme.spacing.md,
    },
    welcomeSection: {
      flex: 1,
    },
    welcomeText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      fontWeight: theme.fontWeight.medium,
    },
    nameText: {
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.bold,
      color: theme.colors.text,
      marginTop: theme.spacing.xs,
    },

    statusDot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      marginRight: theme.spacing.sm,
    },
    statusText: {
      fontSize: theme.fontSize.sm,
      fontWeight: theme.fontWeight.semibold,
      textTransform: "capitalize",
    },
    statsContainer: {
      flexDirection: "row",
      paddingHorizontal: theme.spacing.lg,
      marginBottom: theme.spacing.lg,
    },
    statCard: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.lg,
      borderRadius: theme.borderRadius.lg,
      alignItems: "center",
      marginHorizontal: theme.spacing.xs,
      ...theme.shadows.sm,
    },
    statNumber: {
      fontSize: theme.fontSize.xxl,
      fontWeight: theme.fontWeight.bold,
      color: theme.colors.primary,
      marginBottom: theme.spacing.xs,
    },
    statLabel: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: "center",
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: theme.spacing.lg,
      marginBottom: theme.spacing.md,
    },
    sectionTitle: {
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.text,
    },
    seeAllText: {
      fontSize: theme.fontSize.md,
      color: theme.colors.primary,
      fontWeight: theme.fontWeight.medium,
    },
    requestCard: {
      backgroundColor: theme.colors.surface,
      marginHorizontal: theme.spacing.lg,
      marginBottom: theme.spacing.md,
      padding: theme.spacing.lg,
      borderRadius: theme.borderRadius.lg,
      ...theme.shadows.sm,
    },
    requestHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    requestTitle: {
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.text,
      flex: 1,
      textTransform: "capitalize",
    },
    statusBadge: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
    },
    statusBadgeText: {
      fontSize: theme.fontSize.xs,
      color: "#FFFFFF",
      fontWeight: theme.fontWeight.medium,
    },
    requestDescription: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginBottom: theme.spacing.md,
    },
    requestFooter: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    locationInfo: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    locationText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginLeft: theme.spacing.xs,
    },
    timeText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textTertiary,
    },
    emptyState: {
      alignItems: "center",
      paddingVertical: theme.spacing.xxl,
      paddingHorizontal: theme.spacing.lg,
    },
    emptyStateText: {
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.medium,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.sm,
    },
    emptyStateSubtext: {
      fontSize: theme.fontSize.md,
      color: theme.colors.textTertiary,
      textAlign: "center",
    },
    testButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.surface,
      justifyContent: "center",
      alignItems: "center",
      marginLeft: theme.spacing.sm,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    liveMapContainer: {
      marginHorizontal: theme.spacing.md,
      marginVertical: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      overflow: "hidden",
      ...theme.shadows.md,
    },
    liveMapHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: theme.spacing.md,
      backgroundColor: theme.colors.primary,
    },
    liveMapTitle: {
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.semibold,
      color: "#FFFFFF",
    },
    mapToggleButton: {
      padding: theme.spacing.xs,
    },
    liveMap: {
      height: 300,
    },
    quickActions: {
      flexDirection: "row",
      padding: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      justifyContent: "space-around",
    },

    // Modal Styles
    modalOverlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      justifyContent: "center",
      alignItems: "center",
      zIndex: 1000,
    },
    serviceRequestModal: {
      backgroundColor: theme.colors.surface,
      marginHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.xl,
      ...theme.shadows.xl,
      maxWidth: 400,
      width: "90%",
    },
    modalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    modalTitle: {
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.bold,
      color: theme.colors.text,
    },
    closeButton: {
      padding: theme.spacing.sm,
    },
    modalContent: {
      padding: theme.spacing.lg,
    },
    requestInfo: {
      marginBottom: theme.spacing.md,
    },
    requestIdLabel: {
      fontSize: theme.fontSize.sm,
      fontWeight: theme.fontWeight.medium,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs,
    },
    requestIdText: {
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.bold,
      color: theme.colors.primary,
      marginBottom: theme.spacing.xs,
    },
    validationWarning: {
      fontSize: theme.fontSize.xs,
      color: theme.colors.warning,
      fontStyle: "italic",
    },
    driverLabel: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs,
    },
    driverName: {
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.text,
    },
    driverPhone: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
    },
    locationLabel: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs,
    },
    locationText: {
      fontSize: theme.fontSize.md,
      color: theme.colors.text,
    },
    issueLabel: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs,
    },
    issueText: {
      fontSize: theme.fontSize.md,
      color: theme.colors.text,
    },
    urgencyLabel: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs,
    },
    urgencyBadge: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.md,
      alignSelf: "flex-start",
    },
    urgencyText: {
      fontSize: theme.fontSize.sm,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.surface,
      textTransform: "capitalize",
    },
    modalActions: {
      flexDirection: "row",
      paddingHorizontal: theme.spacing.lg,
      paddingBottom: theme.spacing.lg,
      gap: theme.spacing.md,
    },
    rejectButton: {
      flex: 1,
      backgroundColor: theme.colors.border,
      paddingVertical: theme.spacing.md,
      borderRadius: theme.borderRadius.lg,
      alignItems: "center",
    },
    rejectButtonText: {
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.textSecondary,
    },
    acceptButton: {
      flex: 1,
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.md,
      borderRadius: theme.borderRadius.lg,
      alignItems: "center",
      ...theme.shadows.sm,
    },
    acceptButtonText: {
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.bold,
      color: theme.colors.surface,
    },

    // Close Dialog Button Styles
    closeDialogButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      marginTop: theme.spacing.md + 4, // Added 4px for better visual spacing
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    closeDialogText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginLeft: theme.spacing.xs,
    },

    // Bottom Sheet Styles
    bottomSheet: {
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: theme.colors.surface,
      borderTopLeftRadius: theme.borderRadius.xl,
      borderTopRightRadius: theme.borderRadius.xl,
      paddingTop: theme.spacing.lg,
      paddingHorizontal: theme.spacing.lg,
      paddingBottom: Math.max(insets.bottom, 80) + theme.spacing.lg, // Ensure enough space for tab bar
      ...theme.shadows.xl,
      zIndex: 8,
      maxHeight: "70%", // Limit height to prevent overflow
    },
    driverInfoSection: {
      marginBottom: theme.spacing.lg,
    },
    driverHeader: {
      flexDirection: "row",
      alignItems: "center",
    },
    driverAvatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: theme.colors.background,
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.md,
    },
    driverDetails: {
      flex: 1,
    },
    driverNameText: {
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    driverPhoneText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    infoButton: {
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.background,
    },
    chatSection: {
      marginBottom: theme.spacing.lg,
      flex: 1, // Allow chat to expand
    },
    chatHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    chatHeaderText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginLeft: theme.spacing.xs,
      fontWeight: theme.fontWeight.medium,
    },
    chatContainer: {
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.lg,
      height: 200, // Fixed height for embedded chat
      overflow: "hidden",
    },
    chatMessages: {
      flex: 1,
      padding: theme.spacing.md,
      justifyContent: "center",
      alignItems: "center",
    },
    chatWelcomeMessage: {
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.lg,
    },
    chatWelcomeText: {
      fontSize: theme.fontSize.md,
      color: theme.colors.text,
      fontWeight: theme.fontWeight.medium,
      marginTop: theme.spacing.sm,
      textAlign: "center",
    },
    chatWelcomeSubtext: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
      textAlign: "center",
      fontStyle: "italic",
    },
    chatInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      padding: theme.spacing.sm,
      backgroundColor: theme.colors.surface,
    },
    chatInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      marginRight: theme.spacing.sm,
      fontSize: theme.fontSize.sm,
      color: theme.colors.text,
      backgroundColor: theme.colors.background,
      maxHeight: 90,
      height: 40,
    },
    sendButton: {
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.primary,
      height: 40,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      minWidth: 40,
      ...theme.shadows.sm,
    },
    finishServiceButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: theme.colors.success,
      paddingVertical: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      ...theme.shadows.sm,
    },
    finishServiceText: {
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.bold,
      color: theme.colors.surface,
      marginLeft: theme.spacing.sm,
    },

    // ActionSheet Trigger Styles
    actionSheetTrigger: {
      position: "absolute",
      bottom: insets.bottom + theme.spacing.md,
      left: theme.spacing.md,
      right: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.lg,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      ...theme.shadows.lg,
    },
    actionSheetTriggerText: {
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.primary,
      marginLeft: theme.spacing.sm,
    },

    // ActionSheet Styles
    actionSheetContainer: {
      backgroundColor: theme.colors.surface,
      borderTopLeftRadius: theme.borderRadius.xl,
      borderTopRightRadius: theme.borderRadius.xl,
    },
    actionSheetHeader: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    actionSheetHeaderContent: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
    },
    actionSheetTitle: {
      fontSize: theme.fontSize.lg,
      fontWeight: theme.fontWeight.bold,
      color: theme.colors.text,
      flex: 1,
      marginLeft: theme.spacing.sm,
    },
    actionSheetSubtitle: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: "left",
    },
    urgencyIndicator: {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      backgroundColor: theme.colors.warning,
    },
    urgencyText: {
      fontSize: theme.fontSize.xs,
      fontWeight: theme.fontWeight.bold,
      color: theme.colors.surface,
    },
    actionSheetCloseButton: {
      padding: theme.spacing.xs,
    },
    serviceRequestContent: {
      padding: theme.spacing.lg,
    },
    bottomSheetContent: {
      padding: theme.spacing.lg,
      maxHeight: 600, // Limit height for better UX
    },
    dialogActions: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: theme.spacing.lg,
      gap: theme.spacing.md,
    },

    // Images Section Styles
    imagesSection: {
      marginTop: theme.spacing.lg,
    },
    imagesSectionTitle: {
      fontSize: theme.fontSize.md,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    imagesLoadingContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.lg,
    },
    imagesLoadingText: {
      fontSize: theme.fontSize.sm,
      color: theme.colors.textSecondary,
      marginLeft: theme.spacing.sm,
    },
    imagesScrollView: {
      flexGrow: 0,
    },
    imageContainer: {
      marginRight: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      overflow: "hidden",
      position: "relative",
    },
    serviceImage: {
      width: 80,
      height: 80,
      borderRadius: theme.borderRadius.md,
    },
    imageOverlay: {
      position: "absolute",
      top: 4,
      right: 4,
      backgroundColor: "rgba(0, 0, 0, 0.6)",
      borderRadius: 12,
      padding: 4,
    },

    // Chat Message Styles
    messagesScrollView: {
      flex: 1,
      paddingHorizontal: theme.spacing.sm,
    },
    messageContainer: {
      marginVertical: theme.spacing.xs,
      maxWidth: "80%",
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
    },
    mechanicMessage: {
      alignSelf: "flex-end",
      backgroundColor: theme.colors.primary,
    },
    driverMessage: {
      alignSelf: "flex-start",
      backgroundColor: theme.colors.border,
    },
    messageText: {
      fontSize: theme.fontSize.sm,
      lineHeight: 20,
    },
    mechanicMessageText: {
      color: theme.colors.surface,
    },
    driverMessageText: {
      color: theme.colors.text,
    },
    messageTime: {
      fontSize: theme.fontSize.xs,
      marginTop: theme.spacing.xs,
      opacity: 0.7,
    },
    sendButtonDisabled: {
      backgroundColor: theme.colors.border,
    },

    // Connection Status Styles
    connectionContainer: {
      position: "absolute",
      top: insets.top + 80, // Below status container
      right: theme.spacing.md,
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: "rgba(0, 0, 0, 0.7)",
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.md,
    },
    connectionDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: theme.spacing.xs,
    },
    connectionText: {
      fontSize: theme.fontSize.xs,
      color: theme.colors.surface,
      fontWeight: theme.fontWeight.medium,
    },

    // Debug Panel Styles
    debugPanel: {
      position: "absolute",
      top: insets.top + 20,
      left: theme.spacing.md,
      backgroundColor: "rgba(255, 165, 0, 0.8)",
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
    },
    debugText: {
      fontSize: theme.fontSize.xs,
      color: "#000",
      fontWeight: theme.fontWeight.bold,
    },

    // Test Button Styles
    testButton: {
      position: "absolute",
      top: insets.top + 60, // Below debug panel
      left: theme.spacing.md,
      backgroundColor: "rgba(0, 123, 255, 0.8)",
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
    },
    testButtonText: {
      fontSize: theme.fontSize.xs,
      color: "#fff",
      fontWeight: theme.fontWeight.bold,
    },
  });

export default HomeScreen;
