{"cli": {"version": ">= 16.5.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"simulator": true}, "env": {"GOOGLE_MAPS_API_KEY_IOS": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0", "GOOGLE_MAPS_API_KEY_ANDROID": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0", "API_URL": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "SOCKET_URL": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "NODE_ENV": "development"}}, "preview": {"distribution": "internal", "env": {"GOOGLE_MAPS_API_KEY_IOS": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0", "GOOGLE_MAPS_API_KEY_ANDROID": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0", "API_URL": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "NODE_ENV": "preview"}, "ios": {"simulator": false, "buildConfiguration": "Release"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"autoIncrement": true, "env": {"GOOGLE_MAPS_API_KEY_IOS": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0", "GOOGLE_MAPS_API_KEY_ANDROID": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0", "API_URL": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "SOCKET_URL": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "NODE_ENV": "production"}, "ios": {"buildConfiguration": "Release", "simulator": false}, "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}}, "production-apk": {"extends": "production", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./google-play-service-account.json", "track": "production"}}}}