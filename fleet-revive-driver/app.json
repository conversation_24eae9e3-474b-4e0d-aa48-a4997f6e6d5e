{"expo": {"name": "fleet-revive-driver", "slug": "fleet-revive-driver", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"bundleIdentifier": "com.nirmal0210.fleetrevivedriver", "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"NSLocationWhenInUseUsageDescription": "FleetRevive needs access to your location to provide navigation and location-based services.", "NSLocationAlwaysUsageDescription": "FleetRevive needs access to your location in the background for route tracking and updates.", "NSUserTrackingUsageDescription": "FleetRevive uses this permission to send notifications and keep you updated with the latest information.", "UIBackgroundModes": ["fetch", "remote-notification"], "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true, "NSAllowsLocalNetworking": true, "NSExceptionDomains": {"************": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSExceptionMinimumTLSVersion": "TLSv1.0"}}}, "ITSAppUsesNonExemptEncryption": false}, "config": {"googleMapsApiKey": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0"}}, "android": {"package": "com.nirmal0210.fleetrevivedriver", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"], "googleServicesFile": "./google-services.json", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "config": {"googleMaps": {"apiKey": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0"}}}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"apiUrl": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "productionApiUrl": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "socketUrl": "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com", "eas": {"projectId": "************************************"}}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location.", "locationAlwaysPermission": "Allow $(PRODUCT_NAME) to use your location.", "locationWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with mechanics."}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "defaultChannel": "default"}], "expo-localization", ["expo-maps", {"googleMapsApiKey": "AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0"}]], "notification": {"icon": "./assets/notification-icon.png", "color": "#ffffff"}}}