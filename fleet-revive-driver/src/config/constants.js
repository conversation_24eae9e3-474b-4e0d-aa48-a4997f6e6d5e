// Configuration constants for the driver app

// Google Maps API Key
export const GOOGLE_MAPS_API_KEY = 'AIzaSyCqk1T9tI_hzlami74zS1SuxIvhcEinms0';

// API Configuration - Production URL
export const API_CONFIG = {
  BASE_URL: 'https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com/api',
  SOCKET_URL: 'https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Location Configuration
export const LOCATION_CONFIG = {
  UPDATE_INTERVAL: 5000, // 5 seconds
  HIGH_ACCURACY: true,
  TIMEOUT: 10000,
  MAXIMUM_AGE: 60000, // 1 minute
  DISTANCE_FILTER: 10, // 10 meters
};

// Map Configuration
export const MAP_CONFIG = {
  DEFAULT_REGION: {
    latitude: 43.6532,
    longitude: -79.3832,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  ZOOM_LEVELS: {
    CITY: 0.1,
    NEIGHBORHOOD: 0.01,
    STREET: 0.001,
  },
  MARKER_SIZES: {
    SMALL: 20,
    MEDIUM: 30,
    LARGE: 40,
  },
};

// Service Request Status
export const SERVICE_STATUS = {
  PENDING: 'pending',
  ASSIGNED: 'assigned',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
};

// Driver Status
export const DRIVER_STATUS = {
  AVAILABLE: 'available',
  BUSY: 'busy',
  OFFLINE: 'offline',
  REQUESTING_SERVICE: 'requesting_service',
};

// Notification Types
export const NOTIFICATION_TYPES = {
  SERVICE_ASSIGNED: 'service_assigned',
  MECHANIC_ARRIVED: 'mechanic_arrived',
  SERVICE_COMPLETED: 'service_completed',
  CHAT_MESSAGE: 'chat_message',
  SYSTEM_ALERT: 'system_alert',
};

// Chat Configuration
export const CHAT_CONFIG = {
  MAX_MESSAGE_LENGTH: 500,
  TYPING_TIMEOUT: 3000,
  MESSAGE_BATCH_SIZE: 50,
  AUTO_SCROLL_DELAY: 100,
};

// Theme Configuration
export const THEME_CONFIG = {
  ANIMATION_DURATION: 300,
  BORDER_RADIUS: {
    SMALL: 4,
    MEDIUM: 8,
    LARGE: 12,
    EXTRA_LARGE: 16,
  },
  SPACING: {
    XS: 4,
    SM: 8,
    MD: 16,
    LG: 24,
    XL: 32,
    XXL: 48,
  },
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  LOCATION_PERMISSION: 'Location permission is required to use this feature.',
  LOCATION_UNAVAILABLE: 'Unable to get your current location.',
  SERVICE_REQUEST_FAILED: 'Failed to create service request. Please try again.',
  CHAT_SEND_FAILED: 'Failed to send message. Please try again.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  SERVICE_REQUESTED: 'Service request submitted successfully!',
  MESSAGE_SENT: 'Message sent successfully!',
  LOCATION_UPDATED: 'Location updated successfully!',
};

// Route Configuration
export const ROUTE_CONFIG = {
  DEFAULT_MODE: 'driving',
  AVOID_TOLLS: false,
  AVOID_HIGHWAYS: false,
  AVOID_FERRIES: true,
  OPTIMIZE_WAYPOINTS: true,
  ALTERNATIVES: false,
  LANGUAGE: 'en',
  UNITS: 'metric',
};

// Performance Configuration
export const PERFORMANCE_CONFIG = {
  MAP_RENDER_DELAY: 100,
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 1000,
  MAX_MARKERS: 50,
  MAX_ROUTE_POINTS: 1000,
};

// Service Types
export const SERVICE_TYPES = {
  BREAKDOWN: 'breakdown',
  MAINTENANCE: 'maintenance',
  INSPECTION: 'inspection',
  EMERGENCY: 'emergency',
  TOWING: 'towing',
};

// Priority Levels
export const PRIORITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent',
  EMERGENCY: 'emergency',
};

export default {
  GOOGLE_MAPS_API_KEY,
  API_CONFIG,
  LOCATION_CONFIG,
  MAP_CONFIG,
  SERVICE_STATUS,
  DRIVER_STATUS,
  NOTIFICATION_TYPES,
  CHAT_CONFIG,
  THEME_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  ROUTE_CONFIG,
  PERFORMANCE_CONFIG,
  SERVICE_TYPES,
  PRIORITY_LEVELS,
};
