import axios from "axios";
import { Platform } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Simple network connectivity check using a quick API call
const checkNetworkConnectivity = async () => {
  try {
    // Make a quick HEAD request to check connectivity
    const response = await fetch("https://www.google.com", {
      method: "HEAD",
      timeout: 5000,
    });
    const isConnected = response.ok;
    console.log(
      "🌐 Network connectivity:",
      isConnected ? "Connected" : "Disconnected"
    );
    return isConnected;
  } catch (error) {
    console.warn("⚠️ Network check failed:", error.message);
    return true; // Assume connected if check fails to avoid blocking requests
  }
};

// Production URL configuration
const BASE_URL = "https://fleet-revive-app-a56ae2ba3bc1.herokuapp.com/api";

// Debug logging for network configuration
console.log("🌐 API Configuration:");
console.log("🌐 Platform:", Platform.OS);
console.log("🌐 BASE_URL:", BASE_URL);
console.log("🌐 __DEV__:", __DEV__);

// Create axios instance with unified configuration
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // Increased timeout to 30 seconds for mobile network conditions
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token automatically
api.interceptors.request.use(
  async (config) => {
    try {
      console.log(
        "🌐 API Request:",
        config.method?.toUpperCase(),
        config.baseURL + config.url
      );

      // Optional: Check network connectivity before making request
      try {
        const isConnected = await checkNetworkConnectivity();
        if (!isConnected) {
          console.warn(
            "⚠️ Network connectivity check failed, but proceeding with request"
          );
        }
      } catch (networkError) {
        console.warn(
          "⚠️ Network check failed, proceeding anyway:",
          networkError.message
        );
      }

      const token = await AsyncStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error("Error in request interceptor:", error);
      if (error.message === "No internet connection available") {
        return Promise.reject(
          new Error("Please check your internet connection and try again")
        );
      }
    }
    return config;
  },
  (error) => {
    console.error("❌ Request interceptor error:", error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    console.log("✅ API Response:", response.status, response.config.url);
    return response;
  },
  async (error) => {
    console.error("❌ API Error:", error.message);
    console.error("❌ Error Code:", error.code);
    console.error("❌ Platform:", Platform.OS);
    console.error(
      "❌ Error Response:",
      error.response?.status,
      error.response?.data
    );
    console.error("❌ Error Config:", error.config?.url);
    console.error("❌ Full Error Object:", JSON.stringify(error, null, 2));

    // Handle specific error types
    if (error.code === "ECONNABORTED") {
      console.error("🕐 Request timeout - server may be slow or unreachable");
      const timeoutError = new Error(
        "Request timeout. Please check your connection and try again."
      );
      timeoutError.code = "TIMEOUT";
      return Promise.reject(timeoutError);
    }

    if (error.code === "NETWORK_ERROR" || error.message === "Network Error") {
      console.error("🌐 Network error - check internet connection");
      const networkError = new Error(
        "Network error. Please check your internet connection."
      );
      networkError.code = "NETWORK_ERROR";
      return Promise.reject(networkError);
    }

    if (error.response?.status === 401) {
      // Token expired or invalid, clear storage
      try {
        await AsyncStorage.removeItem("token");
        await AsyncStorage.removeItem("user");
        console.log("🚪 401 Error: Cleared token and user from AsyncStorage");
        // Note: Navigation to login should be handled by the calling component
      } catch (storageError) {
        console.error(
          "Error clearing auth data from AsyncStorage:",
          storageError
        );
      }
    }
    return Promise.reject(error);
  }
);

// ===== NOTES =====
// All API methods have been moved to services.js for better organization
// This file now only contains the configured axios instance with interceptors
// Import specific services from './services' instead of using methods from this file

// Test function for iOS network debugging
export const testNetworkConnection = async () => {
  try {
    console.log("🧪 Testing network connection...");
    console.log("🧪 Target URL:", BASE_URL);

    const response = await fetch(`${BASE_URL.replace("/api", "")}/health`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 10000,
    });

    console.log("🧪 Fetch response status:", response.status);
    const text = await response.text();
    console.log("🧪 Fetch response text:", text);

    return { success: true, status: response.status, data: text };
  } catch (error) {
    console.error("🧪 Network test failed:", error);
    return { success: false, error: error.message };
  }
};

export default api;
