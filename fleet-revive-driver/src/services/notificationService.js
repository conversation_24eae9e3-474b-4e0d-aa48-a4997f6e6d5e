import { Platform } from 'react-native';

// Conditionally import Expo modules to handle missing native modules
let Notifications, Device;
try {
  Notifications = require('expo-notifications');
  Device = require('expo-device');
} catch (error) {
  console.warn('Expo notifications not available:', error.message);
  // Create mock objects for development
  Notifications = {
    setNotificationHandler: () => {},
    getPermissionsAsync: () => Promise.resolve({ status: 'granted' }),
    requestPermissionsAsync: () => Promise.resolve({ status: 'granted' }),
    getExpoPushTokenAsync: () => Promise.resolve({ data: 'mock-token' }),
    setNotificationChannelAsync: () => Promise.resolve(),
    addNotificationReceivedListener: () => ({ remove: () => {} }),
    addNotificationResponseReceivedListener: () => ({ remove: () => {} }),
    scheduleNotificationAsync: () => Promise.resolve('mock-id'),
    cancelScheduledNotificationAsync: () => Promise.resolve(),
    cancelAllScheduledNotificationsAsync: () => Promise.resolve(),
    getBadgeCountAsync: () => Promise.resolve(0),
    setBadgeCountAsync: () => Promise.resolve(),
    removeNotificationSubscription: () => {},
    AndroidImportance: {
      DEFAULT: 'default',
      HIGH: 'high',
      MAX: 'max',
    },
  };
  Device = {
    isDevice: false,
  };
}

// Notification tone configuration
const NOTIFICATION_TONES = {
  INFORMATIVE: {
    sound: 'default',
    priority: Notifications.AndroidImportance.DEFAULT,
    vibrationPattern: [0, 250, 250, 250],
    lightColor: '#4A90E2',
    channelId: 'general',
  },
  URGENT: {
    sound: 'default',
    priority: Notifications.AndroidImportance.HIGH,
    vibrationPattern: [0, 500, 200, 500],
    lightColor: '#FF6B35',
    channelId: 'service-updates',
  },
  SUCCESS: {
    sound: 'default',
    priority: Notifications.AndroidImportance.DEFAULT,
    vibrationPattern: [0, 200, 100, 200],
    lightColor: '#4CAF50',
    channelId: 'general',
  },
  EMERGENCY: {
    sound: 'default',
    priority: Notifications.AndroidImportance.MAX,
    vibrationPattern: [0, 1000, 500, 1000, 500, 1000],
    lightColor: '#FF0000',
    channelId: 'emergency',
  },
};

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
  constructor() {
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
    this.listeners = new Map();
  }

  async initialize() {
    try {
      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Push notification permission not granted');
        return false;
      }

      // Get push token
      if (Device.isDevice) {
        this.expoPushToken = await this.getExpoPushToken();
        console.log('Expo push token:', this.expoPushToken);
      } else {
        console.warn('Must use physical device for push notifications');
      }

      // Set up notification channels for Android
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }

      // Set up listeners
      this.setupListeners();

      return true;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return false;
    }
  }

  async getExpoPushToken() {
    try {
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: 'your-expo-project-id', // Replace with your actual project ID
      });
      return token.data;
    } catch (error) {
      console.error('Error getting Expo push token:', error);
      return null;
    }
  }

  async setupAndroidChannels() {
    // Service updates channel (URGENT tone)
    await Notifications.setNotificationChannelAsync('service-updates', {
      name: 'Service Updates',
      importance: NOTIFICATION_TONES.URGENT.priority,
      vibrationPattern: NOTIFICATION_TONES.URGENT.vibrationPattern,
      lightColor: NOTIFICATION_TONES.URGENT.lightColor,
      sound: NOTIFICATION_TONES.URGENT.sound,
      description: 'Important updates about your service requests',
    });

    // General notifications channel (INFORMATIVE tone)
    await Notifications.setNotificationChannelAsync('general', {
      name: 'General Notifications',
      importance: NOTIFICATION_TONES.INFORMATIVE.priority,
      vibrationPattern: NOTIFICATION_TONES.INFORMATIVE.vibrationPattern,
      lightColor: NOTIFICATION_TONES.INFORMATIVE.lightColor,
      sound: NOTIFICATION_TONES.INFORMATIVE.sound,
      description: 'General app notifications and updates',
    });

    // Emergency channel (EMERGENCY tone)
    await Notifications.setNotificationChannelAsync('emergency', {
      name: 'Emergency Alerts',
      importance: NOTIFICATION_TONES.EMERGENCY.priority,
      vibrationPattern: NOTIFICATION_TONES.EMERGENCY.vibrationPattern,
      lightColor: NOTIFICATION_TONES.EMERGENCY.lightColor,
      sound: NOTIFICATION_TONES.EMERGENCY.sound,
      description: 'Critical emergency alerts requiring immediate attention',
    });

    // Success notifications channel (SUCCESS tone)
    await Notifications.setNotificationChannelAsync('success', {
      name: 'Success Notifications',
      importance: NOTIFICATION_TONES.SUCCESS.priority,
      vibrationPattern: NOTIFICATION_TONES.SUCCESS.vibrationPattern,
      lightColor: NOTIFICATION_TONES.SUCCESS.lightColor,
      sound: NOTIFICATION_TONES.SUCCESS.sound,
      description: 'Positive confirmations and completed actions',
    });
  }

  setupListeners() {
    // Listener for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('Notification received:', notification);
        this.notifyListeners('notification_received', notification);
      }
    );

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('Notification response:', response);
        this.handleNotificationResponse(response);
      }
    );
  }

  handleNotificationResponse(response) {
    const { notification } = response;
    const data = notification.request.content.data;

    // Handle different notification types
    switch (data?.type) {
      case 'service_update':
        this.notifyListeners('navigate_to_service', { serviceId: data.serviceId });
        break;
      case 'new_message':
        this.notifyListeners('navigate_to_chat', { serviceId: data.serviceId });
        break;
      case 'service_completed':
        this.notifyListeners('navigate_to_rating', { serviceId: data.serviceId });
        break;
      default:
        this.notifyListeners('notification_tapped', { data });
        break;
    }
  }

  async scheduleLocalNotification(title, body, data = {}, options = {}) {
    try {
      // Determine tone based on notification type or explicit tone
      const toneType = options.tone || this.getNotificationTone(data.type);
      const tone = NOTIFICATION_TONES[toneType] || NOTIFICATION_TONES.INFORMATIVE;

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: tone.sound,
          priority: tone.priority,
        },
        trigger: options.trigger || null,
        identifier: options.identifier,
      });

      console.log(`📱 Scheduled ${toneType} notification: "${title}"`);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      return null;
    }
  }

  // Determine appropriate notification tone based on type
  getNotificationTone(notificationType) {
    const toneMap = {
      'emergency': 'EMERGENCY',
      'service_request_accepted': 'SUCCESS',
      'service_completed': 'SUCCESS',
      'mechanic_assigned': 'URGENT',
      'mechanic_arriving': 'URGENT',
      'service_update': 'URGENT',
      'new_message': 'INFORMATIVE',
      'general': 'INFORMATIVE',
    };

    return toneMap[notificationType] || 'INFORMATIVE';
  }

  async showServiceUpdateNotification(serviceData) {
    const { serviceType, status, mechanicName } = serviceData;
    
    let title, body;
    
    switch (status) {
      case 'accepted':
        title = 'Service Request Accepted!';
        body = `${mechanicName} has accepted your ${serviceType} service request.`;
        break;
      case 'in_progress':
        title = 'Service Started';
        body = `${mechanicName} has started working on your ${serviceType} service.`;
        break;
      case 'completed':
        title = 'Service Completed';
        body = `Your ${serviceType} service has been completed. Please rate your experience.`;
        break;
      case 'cancelled':
        title = 'Service Cancelled';
        body = `Your ${serviceType} service request has been cancelled.`;
        break;
      default:
        title = 'Service Update';
        body = `Your ${serviceType} service status has been updated.`;
    }

    return await this.scheduleLocalNotification(title, body, {
      type: 'service_update',
      serviceId: serviceData.id,
      status,
    }, {
      tone: 'URGENT',
      identifier: `service_update_${serviceData.id}`,
    });
  }

  async showMechanicArrivingNotification(serviceData, estimatedMinutes) {
    const { serviceType, mechanicName } = serviceData;
    
    return await this.scheduleLocalNotification(
      'Mechanic Arriving Soon! 🚗',
      `${mechanicName} will arrive in approximately ${estimatedMinutes} minutes for your ${serviceType} service.`,
      {
        type: 'mechanic_arriving',
        serviceId: serviceData.id,
      },
      {
        tone: 'URGENT',
        identifier: `mechanic_arriving_${serviceData.id}`,
      }
    );
  }

  async showNewMessageNotification(serviceData, message) {
    const { mechanicName } = serviceData;
    
    return await this.scheduleLocalNotification(
      `💬 Message from ${mechanicName}`,
      message,
      {
        type: 'new_message',
        serviceId: serviceData.id,
      },
      {
        tone: 'INFORMATIVE',
        identifier: `message_${serviceData.id}_${Date.now()}`,
      }
    );
  }

  async showEmergencyNotification(title, body, data = {}) {
    return await this.scheduleLocalNotification(`🚨 ${title}`, body, {
      type: 'emergency',
      ...data,
    }, {
      tone: 'EMERGENCY',
      identifier: `emergency_${Date.now()}`,
    });
  }

  async showSuccessNotification(title, body, data = {}) {
    return await this.scheduleLocalNotification(`✅ ${title}`, body, {
      type: 'success',
      ...data,
    }, {
      tone: 'SUCCESS',
      identifier: `success_${Date.now()}`,
    });
  }

  async cancelNotification(notificationId) {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error cancelling notification:', error);
    }
  }

  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error cancelling all notifications:', error);
    }
  }

  async getBadgeCount() {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('Error getting badge count:', error);
      return 0;
    }
  }

  async setBadgeCount(count) {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  async clearBadge() {
    await this.setBadgeCount(0);
  }

  getExpoPushToken() {
    return this.expoPushToken;
  }

  // Event listener system
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  notifyListeners(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in notification service event listener:', error);
        }
      });
    }
  }

  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
    this.listeners.clear();
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
