import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  TextInput,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import CustomText from "../components/CustomText";
import { useTranslation } from "react-i18next";
import { AntDesign, Ionicons } from "@expo/vector-icons";
import ImageBackgroundSuspense from "../components/ImageBackgroundSuspense";
import { useTheme } from "../context/ThemeContext";
import KeyboardView from "../components/KeyboardView";
import LanguageSwitcher from "../components/LanguageSwitcher";
import { useDispatch, useSelector } from "react-redux";
import { loginDriver } from "../store/slices/authSlice";

const imageSource = require("../../assets/images/login-image-optimized.jpg");

const LoginScreen = ({ navigation }) => {
  const isLoggedIn = useSelector((state) => state.auth.isLoggedIn);
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const [formData, setFormData] = React.useState({
    email: "",
    password: "",
  });
  const handleInputChange = (name, value) => {
    setFormData({ ...formData, [name]: value });
  };
  const handleSubmit = async () => {
    const { email, password } = formData;
    console.log("Login Called", email, password);
    // Basic Validation
    if (!email || !password) {
      Alert.alert("Validation Error", "Please fill in all fields");
      return;
    }

    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(email)) {
      Alert.alert("Validation Error", "Please enter a valid email address");
      return;
    }
    try {
      await dispatch(loginDriver(formData)).unwrap();
      // Navigation will happen automatically via RootNavigator when isAuthenticated becomes true
      Alert.alert("Login Successful", "Welcome back! You are now logged in.");
    } catch (error) {
      console.error("Login error:", error);
      Alert.alert(
        "Login Failed",
        error || "Something went wrong. Please try again."
      );
    }
  };

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: "transparent" }}
      edges={[]}
    >
      <KeyboardView>
        <ImageBackgroundSuspense source={imageSource}>
          <View style={styles.container}>
            <View style={styles.backButtonWrapper}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Ionicons name="chevron-back" size={28} color="white" />
              </TouchableOpacity>
              <CustomText bold style={{ fontSize: 25, color: "white" }}>
                FleetConnect
              </CustomText>
            </View>
            <LanguageSwitcher />
            <View style={styles.inputGroup}>
              <TextInput
                style={styles.input}
                placeholder={t("auth.email")}
                onChangeText={(text) => handleInputChange("email", text)}
                value={formData.email}
                keyboardType="email-address"
                returnKeyType="next"
                autoCapitalize="none"
                placeholderTextColor="rgba(0, 0, 0, 0.5)"
                autoCorrect={false}
              />

              <TextInput
                style={styles.input}
                placeholder={t("auth.password")}
                onChangeText={(text) => handleInputChange("password", text)}
                value={formData.password}
                keyboardType="default"
                returnKeyType="done"
                autoCapitalize="none"
                placeholderTextColor="rgba(0, 0, 0, 0.5)"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={styles.button}
                onPress={() => {
                  handleSubmit();
                }}
              >
                <CustomText style={styles.buttonText}>
                  {t("auth.loginButton")}
                </CustomText>
                <Ionicons
                  name="chevron-forward"
                  size={24}
                  color="white"
                  style={styles.buttonIcon}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.googleButton}
                onPress={() => {
                  // Call your Google sign-in logic here
                  console.log("Google Sign-In Pressed");
                }}
              >
                <AntDesign
                  name="google"
                  size={24}
                  color="white"
                  style={styles.googleIcon}
                />
                <CustomText style={styles.buttonText}>
                  {t("driver.signInWithGoogle")}
                </CustomText>
              </TouchableOpacity>
              <View style={styles.registerView}>
                <CustomText style={{ color: "white", marginEnd: 5 }}>
                  {t("auth.dontHaveAccount")}
                </CustomText>
                <TouchableOpacity onPress={() => navigation.navigate("Signup")}>
                  <CustomText style={{ color: "white" }}>
                    {t("auth.signup")}
                  </CustomText>
                </TouchableOpacity>
              </View>
            </View>
            <CustomText style={styles.forgotPassword}>
              {t("auth.forgotPassword")}
            </CustomText>
          </View>
        </ImageBackgroundSuspense>
      </KeyboardView>
    </SafeAreaView>
  );
};

export default LoginScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },

  backButtonWrapper: {
    position: "absolute",
    top: 60, // Account for status bar height
    left: 0,
    right: 0,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    zIndex: 1,
  },

  blurView: {
    flex: 1,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },

  backButton: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 24,
    position: "absolute",
    left: 20,
    padding: 10,
  },
  inputGroup: {
    width: "90%",
    position: "absolute",
    bottom: 50,
    marginBottom: 20,
  },
  input: {
    width: "100%",
    fontFamily: "Asap-Regular",
    height: 50,
    paddingHorizontal: 15,
    borderRadius: 15,
    marginBottom: 10,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    color: "black",
    fontSize: 16,
  },
  button: {
    width: "100%",
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 15,
    backgroundColor: "rgba(43, 76, 183, 1)",
  },
  buttonText: {
    color: "white",
    fontSize: 16,
  },
  buttonIcon: {
    position: "absolute",
    right: 15,
  },
  buttonIconText: {
    color: "white",
    fontSize: 16,
  },
  googleButton: {
    width: "100%",
    height: 50,
    borderRadius: 15,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    marginTop: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  googleIcon: {
    marginRight: 10,
  },
  googleButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 20,
    height: "100%",
  },
  registerView: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  forgotPassword: {
    color: "white",
    textAlign: "center",
    marginTop: 10,
    fontSize: 14,
    position: "absolute",
    bottom: 30,
  },
});
