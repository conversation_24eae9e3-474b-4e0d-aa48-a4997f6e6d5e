import React, { useEffect, useRef, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Image,
  TextInput,
  Alert,
  ActivityIndicator,
} from "react-native";
import EnhancedMapView from "../components/EnhancedMapView";
import ChatOverlay from "../components/ChatOverlay";
import ConnectionStatusIndicator from "../components/ConnectionStatusIndicator";
import ServiceRequestWaitingUI from "../components/ServiceRequestWaitingUI";
import ActionSheet from "react-native-actions-sheet";
import { pickImage, takePhoto } from "../utils/helper";
import locationService from "../services/locationService";
import socketService from "../services/socketService";
import { useTheme } from "../context/ThemeContext";
import { useSelector, useDispatch } from "react-redux";
import { selectToken, selectUser } from "../store/slices/authSlice";
import {
  createServiceRequest,
  selectServiceLoading,
  selectActiveRequest,
  selectMechanicLocation,
  updateMechanicLocation,
  setActiveRequest,
  selectWaitingForMechanic,
  selectEstimatedWaitTime,
  setWaitingForMechanic,
  handleMechanicAccepted,
  cancelServiceRequest,
} from "../store/slices/serviceSlice";

const serviceButtons = ["Tire", "Engine", "Tow"];
const { width } = Dimensions.get("window");

export default function App() {
  const [location, setLocation] = useState(null);
  const [serviceType, setServiceType] = useState(null);
  const [images, setImages] = useState([]);
  const [description, setDescription] = useState("");
  const [vehicleDetails, setVehicleDetails] = useState("");
  const [statusText, setStatusText] = useState("Submit");
  const [showChat, setShowChat] = useState(false);
  const [chatMinimized, setChatMinimized] = useState(false);
  const [minimumWaitTimeActive, setMinimumWaitTimeActive] = useState(false);
  const [autoCloseTimer, setAutoCloseTimer] = useState(null);
  const { theme } = useTheme();
  const actionSheetRef = useRef(null);
  const dispatch = useDispatch();

  // Create styles with current theme
  const styles = createStyles(theme);

  // Redux selectors
  const token = useSelector(selectToken);
  const user = useSelector(selectUser);
  const serviceLoading = useSelector(selectServiceLoading);
  const activeRequest = useSelector(selectActiveRequest);
  const mechanicLocation = useSelector(selectMechanicLocation);
  const waitingForMechanic = useSelector(selectWaitingForMechanic);
  const estimatedWaitTime = useSelector(selectEstimatedWaitTime);

  // Debug Redux selectors
  useEffect(() => {
    if (__DEV__) {
      console.log("🔍 Driver App: Redux selectors update:", {
        activeRequest: !!activeRequest,
        activeRequestId: activeRequest?.id,
        activeRequestStatus: activeRequest?.status,
        waitingForMechanic,
        mechanicLocation: !!mechanicLocation,
        estimatedWaitTime,
      });
    }
  }, [activeRequest, waitingForMechanic, mechanicLocation, estimatedWaitTime]);

  // Use Redux loading state instead of local isSubmitting
  const isSubmitting = serviceLoading?.creating || false;

  // Check if user is authenticated and initialize WebSocket
  useEffect(() => {
    if (!user || !token) {
      console.log("User not authenticated, should show login screen");
    } else {
      console.log("User authenticated:", user.name);
      console.log("Full user object:", JSON.stringify(user, null, 2));
      console.log("Token exists:", !!token);

      // Initialize Socket.IO connection for real-time updates
      socketService.connect();

      // Join service room if there's an active request
      if (activeRequest?.id) {
        socketService.joinServiceRoom(activeRequest.id);
      }

      // Set up socket event listeners for UI-specific updates
      const handleMechanicAssigned = (data) => {
        console.log("🔧 Driver App: Mechanic assigned - UI update:", data);
        // Show chat when mechanic is assigned
        setShowChat(true);
        setChatMinimized(true);
      };

      const handleServiceStatusUpdate = (data) => {
        console.log("🔄 Driver App: Service status update - UI update:", data);
        // Handle UI-specific updates based on status
        if (data.status === "completed") {
          setShowChat(false);
          setChatMinimized(false);
        }
      };

      const handleServiceCompleted = (data) => {
        console.log("✅ Driver App: Service completed - UI update:", data);
        setShowChat(false);
        setChatMinimized(false);
      };

      // Add socket event listeners for UI-specific updates
      socketService.addEventListener(
        "mechanic_assigned",
        handleMechanicAssigned
      );
      socketService.addEventListener(
        "service_status_update",
        handleServiceStatusUpdate
      );
      socketService.addEventListener(
        "service_completed",
        handleServiceCompleted
      );

      return () => {
        // Clean up socket event listeners
        socketService.removeEventListener(
          "mechanic_assigned",
          handleMechanicAssigned
        );
        socketService.removeEventListener(
          "service_status_update",
          handleServiceStatusUpdate
        );
        socketService.removeEventListener(
          "service_completed",
          handleServiceCompleted
        );
      };
    }

    return () => {
      // Clean up Socket.IO connection when component unmounts
      socketService.disconnect();
    };
  }, [user, token, activeRequest?.id, dispatch]);

  // Monitor activeRequest changes for UI updates
  useEffect(() => {
    if (__DEV__) {
      console.log("🔄 Driver App: activeRequest changed:", {
        hasActiveRequest: !!activeRequest,
        status: activeRequest?.status,
        mechanicName: activeRequest?.mechanicName,
        mechanicId: activeRequest?.mechanicId,
        id: activeRequest?.id,
        issueType: activeRequest?.issueType,
        waitingForMechanic: waitingForMechanic,
        fullObject: activeRequest,
      });
    }
  }, [activeRequest, waitingForMechanic]);

  // Handle minimum wait time for better UX
  useEffect(() => {
    if (waitingForMechanic && !minimumWaitTimeActive) {
      console.log('⏰ Driver App: Starting minimum wait time (3 seconds)');
      setMinimumWaitTimeActive(true);

      // Set minimum wait time of 3 seconds
      const timer = setTimeout(() => {
        console.log('⏰ Driver App: Minimum wait time completed');
        setMinimumWaitTimeActive(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [waitingForMechanic, minimumWaitTimeActive]);

  // Auto-close waiting UI if no mechanic accepts within 5 minutes
  useEffect(() => {
    if (waitingForMechanic && activeRequest?.status === 'pending') {
      console.log('⏰ Driver App: Starting auto-close timer (5 minutes)');

      const timer = setTimeout(() => {
        console.log('⏰ Driver App: Auto-closing waiting UI - no mechanic accepted');

        Alert.alert(
          "Request Still Open",
          "No mechanic has accepted your request yet, but it remains open for mechanics to bid on. You'll be notified when someone accepts.",
          [
            {
              text: "Keep Waiting",
              style: "default",
              onPress: () => {
                // Restart the timer
                console.log('⏰ Driver App: User chose to keep waiting - restarting timer');
              }
            },
            {
              text: "Cancel Request",
              style: "destructive",
              onPress: () => {
                dispatch(cancelServiceRequest());
                Alert.alert("Request Cancelled", "Your service request has been cancelled.");
              }
            }
          ]
        );

        // Hide the waiting UI but keep the request active
        dispatch(setWaitingForMechanic(false));

      }, 5 * 60 * 1000); // 5 minutes

      setAutoCloseTimer(timer);

      return () => {
        if (timer) {
          clearTimeout(timer);
          setAutoCloseTimer(null);
        }
      };
    }
  }, [waitingForMechanic, activeRequest?.status, dispatch]);

  // Show chat when there's an active request with accepted status
  useEffect(() => {
    if (
      activeRequest &&
      (activeRequest.status === "accepted" ||
        activeRequest.status === "assigned")
    ) {
      console.log("💬 Driver App: Showing chat for accepted/assigned request");
      setShowChat(true);
    } else {
      console.log(
        "💬 Driver App: Hiding chat - no active request or not accepted"
      );
      setShowChat(false);
      setChatMinimized(false);
    }
  }, [activeRequest]);

  // Debug waiting state with timestamp
  useEffect(() => {
    if (__DEV__) {
      const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
      console.log(`⏳ [${timestamp}] Driver App: waitingForMechanic state changed:`, {
        waitingForMechanic,
        hasActiveRequest: !!activeRequest,
        activeRequestStatus: activeRequest?.status,
        activeRequestId: activeRequest?.id,
        shouldShowWaitingUI: waitingForMechanic && !!activeRequest,
        stackTrace: new Error().stack?.split('\n')[2]?.trim(), // Show where this was called from
      });
    }
  }, [waitingForMechanic, activeRequest]);

  // Debug ServiceRequestWaitingUI visibility
  useEffect(() => {
    if (__DEV__) {
      console.log("🔍 Driver App: ServiceRequestWaitingUI visibility check:", {
        visible: waitingForMechanic,
        serviceRequest: activeRequest,
        serviceRequestId: activeRequest?.id,
        serviceRequestStatus: activeRequest?.status,
      });
    }
  }, [waitingForMechanic, activeRequest]);

  useEffect(() => {
    const initializeLocation = async () => {
      try {
        // Get current location
        const currentLocation = await locationService.getCurrentLocation();
        if (currentLocation) {
          setLocation(currentLocation);
        }

        // Set up location listener
        const handleLocationUpdate = (newLocation) => {
          if (newLocation?.coords?.latitude && newLocation?.coords?.longitude) {
            setLocation(newLocation);
          }
        };

        locationService.addEventListener(
          "location_update",
          handleLocationUpdate
        );

        return () => {
          locationService.removeEventListener(
            "location_update",
            handleLocationUpdate
          );
        };
      } catch (error) {
        console.error("Error initializing location:", error);
        Alert.alert(
          "Location Error",
          "Unable to get your location. Please check your location settings."
        );
      }
    };

    initializeLocation();
  }, []);

  useEffect(() => {
    if (images.length > 3) {
      setImages(images.slice(0, 3));
      Alert.alert("Limit reached", "You can only upload up to 3 images.");
    }
  }, [images]);

  // Handle canceling service request
  const handleCancelServiceRequest = async () => {
    try {
      Alert.alert(
        "Cancel Request",
        "Are you sure you want to cancel this service request?",
        [
          { text: "No", style: "cancel" },
          {
            text: "Yes, Cancel",
            style: "destructive",
            onPress: async () => {
              console.log('🚫 Driver App: User confirmed cancel request');
              dispatch(cancelServiceRequest());
              console.log('🚫 Driver App: Cancel action dispatched');
              Alert.alert(
                "Request Cancelled",
                "Your service request has been cancelled."
              );
            },
          },
        ]
      );
    } catch (error) {
      console.error("Error cancelling service request:", error);
      Alert.alert(
        "Error",
        "Failed to cancel service request. Please try again."
      );
    }
  };

  const handleWaitTimeExpired = () => {
    console.log("⏰ Driver App: Wait time expired - extending search");

    // Show alert to inform user
    Alert.alert(
      "Extended Search",
      "We're still looking for available mechanics in your area. We'll notify you as soon as one becomes available.",
      [
        {
          text: "Keep Waiting",
          style: "default"
        },
        {
          text: "Cancel Request",
          style: "destructive",
          onPress: () => {
            dispatch(cancelServiceRequest());
            Alert.alert(
              "Request Cancelled",
              "Your service request has been cancelled."
            );
          }
        }
      ]
    );

    // Optionally extend the search radius or notify backend
    // You could dispatch an action here to extend search parameters
  };

  // Handle mechanic acceptance
  const handleMechanicAcceptance = (mechanicData) => {
    dispatch(handleMechanicAccepted(mechanicData));
    Alert.alert(
      "Mechanic Found!",
      `${mechanicData.mechanic.name} has accepted your request and is on the way.`,
      [{ text: "OK" }]
    );
  };

  const handleSubmit = async () => {
    if (!serviceType) {
      Alert.alert("Service Type Required", "Please select a service type.");
      return;
    }
    if (!description.trim()) {
      Alert.alert("Description Required", "Please describe your issue.");
      return;
    }
    if (!vehicleDetails.trim()) {
      Alert.alert("Vehicle Details Required", "Please enter vehicle details.");
      return;
    }
    if (!location?.coords?.latitude || !location?.coords?.longitude) {
      Alert.alert(
        "Location Required",
        "Unable to get your location. Please check your location settings."
      );
      return;
    }

    setStatusText("Submitting...");

    try {
      const requestData = {
        driverId: user?.id,
        serviceType: serviceType?.toLowerCase(), // Ensure lowercase for backend
        description: description?.trim(),
        vehicleDetails: vehicleDetails?.trim(),
        images: images.map((image) => image.uri),
        location: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
      };

      // Use Redux action to create service request
      if (__DEV__) {
        console.log(
          "🚛 Driver App: Dispatching createServiceRequest action..."
        );
      }
      let result;
      try {
        result = await dispatch(createServiceRequest({ requestData, token }));
        if (__DEV__) {
          console.log("🚛 Driver App: Redux action result:", result);
        }
      } catch (dispatchError) {
        console.error("🚛 Driver App: Redux dispatch error:", dispatchError);
        throw dispatchError;
      }

      if (createServiceRequest.fulfilled.match(result)) {
        setStatusText("Request Sent!");

        // Debug: Check state after service request creation
        if (__DEV__) {
          console.log("🚛 Driver App: Service request created successfully:", {
            resultPayload: result.payload,
            serviceRequestId: result.payload?.serviceRequest?.id,
            serviceRequestStatus: result.payload?.serviceRequest?.status,
          });

          // Check Redux state after a brief delay
          setTimeout(() => {
            console.log("🚛 Driver App: Redux state after service creation:", {
              waitingForMechanic,
              activeRequest,
              activeRequestId: activeRequest?.id,
              activeRequestStatus: activeRequest?.status,
            });
          }, 100);
        }

        // Join service room for real-time updates first
        if (result.payload?.serviceRequest?.id) {
          console.log(
            "🏠 Driver App: Joining service room:",
            result.payload.serviceRequest.id
          );
          socketService.joinServiceRoom(result.payload.serviceRequest.id);
        }

        // Show alert after a brief delay to allow UI to update
        // setTimeout(() => {
        //   Alert.alert(
        //     "Request Sent",
        //     "Your service request has been sent to nearby providers. You'll be notified when a mechanic accepts your request."
        //   );
        // }, 500);

        // Reset form
        setServiceType(null);
        setDescription("");
        setVehicleDetails("");
        setImages([]);
        actionSheetRef.current?.hide();

        setTimeout(() => {
          setStatusText("Submit");
        }, 2000);
      } else {
        setStatusText("Submit");
        const errorMessage =
          result.payload || "Failed to submit service request.";
        console.error("Service request failed:", errorMessage);

        // Provide specific error messages
        if (errorMessage.includes("Driver not found")) {
          Alert.alert(
            "Authentication Error",
            "Your driver profile was not found. Please log out and log in again, or contact support if the issue persists."
          );
        } else if (errorMessage.includes("Unauthorized")) {
          Alert.alert(
            "Authentication Error",
            "Your session has expired. Please log out and log in again."
          );
        } else {
          Alert.alert("Error", errorMessage);
        }
      }
    } catch (error) {
      console.error("Error submitting request:", error);
      setStatusText("Submit");
      Alert.alert(
        "Error",
        "Failed to submit service request. Please check your internet connection and try again."
      );
    }
  };

  if (!location?.coords) {
    return (
      <View style={styles.centeredContainer}>
        <Text>Loading location...</Text>
      </View>
    );
  }

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <EnhancedMapView
        style={styles.map}
        userLocation={location}
        mechanicLocation={mechanicLocation}
        activeServiceId={activeRequest?.id}
        showTraffic={true}
        showNearbyServices={true}
        onMapPress={() => {
          // Handle map press if needed
        }}
      />

      {/* Connection Status Indicator */}
      {/* <ConnectionStatusIndicator /> */}

      {/* Connected State UI */}
      {activeRequest && activeRequest.status === "accepted" ? (
        <View style={styles.connectedStateContainer}>
          {/* Service Status Banner */}
          <View style={[styles.statusBanner, { backgroundColor: "#4CAF50" }]}>
            <Text style={styles.statusBannerText}>
              🔧 Mechanic Assigned - Service in Progress
            </Text>
          </View>

          {/* Mechanic Info Card */}
          <View
            style={[
              styles.mechanicInfoCard,
              { backgroundColor: theme.colors.surface },
            ]}
          >
            <View style={styles.mechanicInfoContent}>
              <View style={styles.mechanicDetails}>
                <Text
                  style={[
                    styles.mechanicName,
                    { color: theme.colors.textPrimary },
                  ]}
                >
                  {activeRequest.mechanicName || "Mechanic"}
                </Text>
                <Text
                  style={[
                    styles.serviceTypeText,
                    { color: theme.colors.textSecondary },
                  ]}
                >
                  {activeRequest.serviceType || activeRequest.issue_type}{" "}
                  Service
                </Text>
                {mechanicLocation ? (
                  <Text
                    style={[
                      styles.locationStatus,
                      { color: theme.colors.primary },
                    ]}
                  >
                    📍 Mechanic is on the way
                  </Text>
                ) : (
                  <Text
                    style={[
                      styles.locationStatus,
                      { color: theme.colors.textSecondary },
                    ]}
                  >
                    📍 Waiting for mechanic location...
                  </Text>
                )}
                <Text
                  style={[
                    styles.serviceId,
                    { color: theme.colors.textTertiary },
                  ]}
                >
                  Service ID: {activeRequest.id}
                </Text>
              </View>
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={[
                    styles.chatButton,
                    { backgroundColor: theme.colors.primary },
                  ]}
                  onPress={() => {
                    if (showChat) {
                      setChatMinimized(!chatMinimized);
                    } else {
                      setShowChat(true);
                      setChatMinimized(false);
                    }
                  }}
                >
                  <Text style={[styles.chatButtonText, { color: "white" }]}>
                    💬 {showChat && !chatMinimized ? "Hide" : "Chat"}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.callButton, { backgroundColor: "#4CAF50" }]}
                  onPress={() => {
                    // TODO: Implement call functionality
                    Alert.alert(
                      "Call Mechanic",
                      "Calling feature will be implemented soon."
                    );
                  }}
                >
                  <Text style={[styles.callButtonText, { color: "white" }]}>
                    📞 Call
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      ) : (
        // Show service request buttons when no active request
        <View style={styles.buttonGroup}>
          {serviceButtons.map((label) => (
            <TouchableOpacity
              key={label}
              style={styles.actionButton}
              onPress={() => {
                setServiceType(label);
                actionSheetRef.current?.show();
              }}
            >
              <Text style={styles.buttonText}>{label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      <ActionSheet
        ref={actionSheetRef}
        closable={!isSubmitting}
        gestureEnabled={!isSubmitting}
      >
        <View style={styles.sheetContent}>
          <Text style={styles.serviceTypeMainText}>
            You selected:{" "}
            <Text style={styles.serviceTypeBold}>
              {serviceType || "Service"}
            </Text>
          </Text>
          <Text style={styles.serviceTypeText}>
            Tell us more about your {serviceType || "service"} issue.
          </Text>
          <TextInput
            style={[
              styles.textInput,
              {
                borderColor: theme.colors.border,
                color: theme.colors.text,
                backgroundColor: theme.colors.surface,
              },
            ]}
            placeholder="Describe your issue"
            placeholderTextColor={theme.colors.textTertiary}
            returnKeyType="done"
            value={description}
            onChangeText={setDescription}
            name="description"
          />
          <TextInput
            style={[
              styles.textInput,
              {
                borderColor: theme.colors.border,
                color: theme.colors.text,
                backgroundColor: theme.colors.surface,
              },
            ]}
            placeholder="Enter Vehicle Number"
            placeholderTextColor={theme.colors.textTertiary}
            returnKeyType="done"
            value={vehicleDetails}
            onChangeText={setVehicleDetails}
            name="vehicleDetails"
          />
          <Text
            style={[
              styles.serviceTypeText,
              { color: theme.colors.textPrimary },
            ]}
          >
            Attach up to 3 Images of your {serviceType || "service"} issue
          </Text>
          <View style={styles.imageButtonGroup}>
            <TouchableOpacity
              onPress={() => {
                try {
                  pickImage(setImages);
                } catch (error) {
                  console.error("Error picking image:", error);
                  Alert.alert(
                    "Error",
                    "Failed to pick image. Please try again."
                  );
                }
              }}
              style={styles.imageButton}
            >
              <Text style={styles.imageButtonText}>Pick Image</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                try {
                  takePhoto(setImages);
                } catch (error) {
                  console.error("Error taking photo:", error);
                  Alert.alert(
                    "Error",
                    "Failed to take photo. Please try again."
                  );
                }
              }}
              style={styles.imageButton}
            >
              <Text style={styles.imageButtonText}>Take Photo</Text>
            </TouchableOpacity>
          </View>

          {images.length > 0 && (
            <>
              <View style={styles.previewContainer}>
                {images.map((image, index) => (
                  <Image
                    key={index}
                    source={{ uri: image.uri }}
                    style={styles.imagePreview}
                  />
                ))}
              </View>
            </>
          )}
          <View style={styles.processButtonGroup}>
            {!isSubmitting && (
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => {
                  try {
                    actionSheetRef.current?.hide();
                    setImages([]);
                    setServiceType(null);
                    setDescription("");
                    setVehicleDetails("");
                  } catch (error) {
                    console.error("Error closing modal:", error);
                  }
                }}
              >
                <Text style={styles.cancelButtonText}>Close</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[
                styles.submitButton,
                isSubmitting && { width: "100%" }, // Make full width when submitting
              ]}
              onPress={() => {
                handleSubmit();
              }}
              disabled={isSubmitting}
            >
              <View style={styles.submitContent}>
                {isSubmitting && (
                  <ActivityIndicator
                    size="small"
                    color="#fff"
                    style={{ marginRight: 10 }}
                  />
                )}
                <Text style={styles.submitButtonText}>{statusText}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </ActionSheet>

      {/* Chat Overlay */}
      <ChatOverlay
        visible={showChat}
        onClose={() => setShowChat(false)}
        serviceId={activeRequest?.id}
        mechanicName={activeRequest?.mechanicName || "Mechanic"}
        isMinimized={chatMinimized}
        onToggleMinimize={() => setChatMinimized(!chatMinimized)}
      />

      {/* Service Request Waiting UI */}
      <ServiceRequestWaitingUI
        visible={waitingForMechanic || minimumWaitTimeActive} // Show during waiting OR minimum wait time
        serviceRequest={activeRequest || (__DEV__ ? {
          id: 'test-123',
          status: 'pending',
          issueType: 'engine',
          issueDescription: 'Test service request',
          location: { latitude: 43.6532, longitude: -79.3832 }
        } : null)}
        onCancel={handleCancelServiceRequest}
        onMechanicAccepted={handleMechanicAcceptance}
        onWaitTimeExpired={handleWaitTimeExpired}
        estimatedWaitTime={estimatedWaitTime}
      />

      {/* Debug Info Overlay */}
      {__DEV__ && (
        <View style={{
          position: 'absolute',
          top: 100,
          left: 20,
          backgroundColor: 'rgba(0,0,0,0.8)',
          padding: 10,
          borderRadius: 5,
          zIndex: 1000,
        }}>
          <Text style={{ color: 'white', fontSize: 12 }}>
            DEBUG INFO:{'\n'}
            waitingForMechanic: {String(waitingForMechanic)}{'\n'}
            activeRequest: {activeRequest ? 'YES' : 'NO'}{'\n'}
            activeRequest.id: {activeRequest?.id || 'null'}{'\n'}
            activeRequest.status: {activeRequest?.status || 'null'}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: 'blue',
              padding: 8,
              borderRadius: 4,
              marginTop: 10,
            }}
            onPress={() => {
              console.log('🧪 Manual test: Setting waiting state');
              dispatch(setWaitingForMechanic(true));
              dispatch(setActiveRequest({
                id: 'manual-test-123',
                status: 'pending',
                issueType: 'engine',
                issueDescription: 'Manual test request',
                location: { latitude: 43.6532, longitude: -79.3832 }
              }));
            }}
          >
            <Text style={{ color: 'white', fontSize: 10 }}>
              TEST WAITING UI
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const createStyles = (theme) =>
  StyleSheet.create({
    container: { flex: 1 },
    centeredContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    actionButton: {
      backgroundColor: theme.colors.surface,
      paddingVertical: 13,
      paddingHorizontal: 15,
      width: width / 3 - 20,
      borderRadius: 25,
      ...theme.shadows.md,
      alignItems: "center",
      justifyContent: "center",
    },
    buttonText: {
      fontSize: 16,
      fontWeight: "bold",
      color: theme.colors.text,
    },
    map: { flex: 1 },
    buttonGroup: {
      position: "absolute",
      top: 70, // Increased from 60 to avoid status bar overlap
      flexDirection: "row",
      justifyContent: "space-around",
      width: "100%",
      paddingHorizontal: 15, // Increased padding for better spacing
      zIndex: 10,
    },
    processButtonGroup: {
      flexDirection: "row",
      justifyContent: "center",
      width: "100%",
      gap: 10,
    },
    cancelButton: {
      backgroundColor: theme.colors.surface,
      paddingVertical: 13,
      paddingHorizontal: 15,
      width: "48%",
      borderRadius: 25,
      ...theme.shadows.md,
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    submitButton: {
      backgroundColor: theme.colors.primary,
      paddingVertical: 13,
      paddingHorizontal: 15,
      borderRadius: 25,
      width: "48%", // Default, will be overridden to 100% when isSubmitting
      ...theme.shadows.md,
      alignItems: "center",
      justifyContent: "center",
    },
    submitContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },

    cancelButtonText: {
      fontSize: 16,
      fontWeight: "bold",
      color: theme.colors.text,
    },
    submitButtonText: {
      fontSize: 16,
      fontWeight: "bold",
      color: theme.colors.white,
    },
    sheetContent: { padding: 20, alignItems: "center" },
    serviceTypeMainText: {
      fontSize: 20,
      marginBottom: 20,
      textAlign: "center",
    },
    serviceTypeText: { fontSize: 15, marginBottom: 20 },
    serviceTypeBold: { fontWeight: "bold" },
    textInput: {
      width: "100%",
      height: 50,
      borderWidth: 1,
      borderRadius: 10,
      paddingHorizontal: 15,
      marginBottom: 20,
      fontSize: 16,
    },
    imageButtonGroup: {
      flexDirection: "row",
      justifyContent: "center",
      width: "100%",
      marginBottom: 20,
      gap: 10,
    },
    imageButton: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      padding: 10,
      borderRadius: 10,
      alignItems: "center",
      marginHorizontal: 5,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    imageButtonText: { fontSize: 16, color: theme.colors.text },
    previewContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "center",
      marginBottom: 20,
    },
    imagePreview: {
      width: 100,
      height: 100,
      margin: 5,
      borderRadius: 10,
    },
    // Connected State Styles
    connectedStateContainer: {
      position: "absolute",
      top: 70,
      left: 15,
      right: 15,
      zIndex: 10,
    },
    statusBanner: {
      paddingVertical: 8,
      paddingHorizontal: 15,
      borderRadius: 8,
      marginBottom: 10,
      alignItems: "center",
    },
    statusBannerText: {
      color: "white",
      fontSize: 14,
      fontWeight: "bold",
    },
    mechanicInfoCard: {
      borderRadius: 15,
      padding: 15,
      ...theme.shadows.md,
    },
    mechanicInfoContent: {
      flexDirection: "column",
    },
    mechanicDetails: {
      marginBottom: 15,
    },
    mechanicName: {
      fontSize: 18,
      fontWeight: "bold",
      marginBottom: 4,
    },
    serviceTypeText: {
      fontSize: 14,
      marginBottom: 4,
    },
    locationStatus: {
      fontSize: 12,
      fontStyle: "italic",
      marginBottom: 4,
    },
    serviceId: {
      fontSize: 10,
      fontStyle: "italic",
    },
    actionButtons: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 10,
    },
    chatButton: {
      flex: 1,
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderRadius: 20,
      alignItems: "center",
    },
    chatButtonText: {
      fontSize: 14,
      fontWeight: "bold",
    },
    callButton: {
      flex: 1,
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderRadius: 20,
      alignItems: "center",
    },
    callButtonText: {
      fontSize: 14,
      fontWeight: "bold",
    },
  });
