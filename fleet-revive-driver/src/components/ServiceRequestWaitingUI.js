import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  ActivityIndicator,
  TouchableOpacity,
  Animated,
  Dimensions,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { useTranslation } from 'react-i18next';

const { width, height } = Dimensions.get('window');

const ServiceRequestWaitingUI = ({
  visible,
  serviceRequest,
  onCancel,
  onMechanicAccepted,
  estimatedWaitTime = 5, // minutes
}) => {
  const { theme } = useTheme();
  const { t } = useTranslation();

  // Debug logging for serviceRequest structure
  useEffect(() => {
    if (__DEV__ && serviceRequest) {
      console.log('🔍 ServiceRequestWaitingUI: serviceRequest structure:', {
        hasLocation: !!serviceRequest.location,
        location: serviceRequest.location,
        locationKeys: serviceRequest.location ? Object.keys(serviceRequest.location) : null,
        latitude: serviceRequest.location?.latitude,
        longitude: serviceRequest.location?.longitude,
        fullServiceRequest: serviceRequest,
      });
    }
  }, [serviceRequest]);

  // Helper function to format location safely
  const formatLocation = (location) => {
    if (!location) return 'Current Location';

    const lat = location.latitude;
    const lng = location.longitude;

    // Handle different possible formats
    if (lat !== undefined && lng !== undefined && lat !== null && lng !== null) {
      try {
        const latNum = typeof lat === 'string' ? parseFloat(lat) : Number(lat);
        const lngNum = typeof lng === 'string' ? parseFloat(lng) : Number(lng);

        if (!isNaN(latNum) && !isNaN(lngNum)) {
          return `${latNum.toFixed(4)}, ${lngNum.toFixed(4)}`;
        }
      } catch (error) {
        console.warn('🔍 Error formatting location:', error);
      }
    }

    return 'Current Location';
  };
  
  const [waitTime, setWaitTime] = useState(estimatedWaitTime * 60); // Convert to seconds
  const [pulseAnim] = useState(new Animated.Value(1));
  const [fadeAnim] = useState(new Animated.Value(0));

  // Pulse animation for the loading indicator
  useEffect(() => {
    if (visible) {
      // Fade in animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Pulse animation
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => {
        pulseAnimation.stop();
      };
    } else {
      // Fade out animation
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, pulseAnim, fadeAnim]);

  // Countdown timer
  useEffect(() => {
    if (!visible || waitTime <= 0) return;

    const timer = setInterval(() => {
      setWaitTime((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [visible, waitTime]);

  // Format time display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get urgency color
  const getUrgencyColor = () => {
    switch (serviceRequest?.urgency) {
      case 'high':
        return '#FF4444';
      case 'medium':
        return '#FF8800';
      default:
        return theme.colors.primary;
    }
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <Animated.View 
        style={[
          styles.overlay,
          { opacity: fadeAnim }
        ]}
      >
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.colors.textPrimary }]}>
              {t('driver.waitingForMechanic', 'Looking for Available Mechanic')}
            </Text>
            <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
              {t('driver.pleaseWait', 'Please wait while we find a mechanic near you')}
            </Text>
          </View>

          {/* Animated Loading Indicator */}
          <View style={styles.loadingContainer}>
            <Animated.View
              style={[
                styles.pulseContainer,
                {
                  transform: [{ scale: pulseAnim }],
                  borderColor: getUrgencyColor(),
                }
              ]}
            >
              <View style={[styles.innerCircle, { backgroundColor: getUrgencyColor() }]}>
                <Ionicons 
                  name="car-sport" 
                  size={40} 
                  color="white" 
                />
              </View>
            </Animated.View>
            
            {/* <ActivityIndicator 
              size="large" 
              color={getUrgencyColor()} 
              style={styles.spinner}
            /> */}
          </View>

          {/* Service Request Info */}
          <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.infoRow}>
              <Ionicons name="build" size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.textPrimary }]}>
                {serviceRequest?.issue_type || serviceRequest?.serviceType || 'Service Request'}
              </Text>
            </View>
            
            <View style={styles.infoRow}>
              <Ionicons name="location" size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
                {formatLocation(serviceRequest?.location)}
              </Text>
            </View>

            {serviceRequest?.urgency && (
              <View style={styles.infoRow}>
                <Ionicons 
                  name={serviceRequest.urgency === 'high' ? 'alert-circle' : 'time'} 
                  size={20} 
                  color={getUrgencyColor()} 
                />
                <Text style={[styles.infoText, { color: getUrgencyColor() }]}>
                  {serviceRequest.urgency.charAt(0).toUpperCase() + serviceRequest.urgency.slice(1)} Priority
                </Text>
              </View>
            )}
          </View>

          {/* Wait Time Display */}
          <View style={styles.timerContainer}>
            <Text style={[styles.timerLabel, { color: theme.colors.textSecondary }]}>
              {t('driver.estimatedWaitTime', 'Estimated Wait Time')}
            </Text>
            <Text style={[styles.timerText, { color: getUrgencyColor() }]}>
              {formatTime(waitTime)}
            </Text>
          </View>

          {/* Status Messages */}
          <View style={styles.statusContainer}>
            <View style={styles.statusItem}>
              <View style={[styles.statusDot, { backgroundColor: theme.colors.success }]} />
              <Text style={[styles.statusText, { color: theme.colors.textSecondary }]}>
                {t('driver.requestSent')}
              </Text>
            </View>
            
            <View style={styles.statusItem}>
              <View style={[styles.statusDot, { backgroundColor: getUrgencyColor() }]} />
              <Text style={[styles.statusText, { color: theme.colors.textSecondary }]}>
                {t('driver.searchingMechanics')}
              </Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: theme.colors.error }]}
              onPress={onCancel}
            >
              <Text style={[styles.cancelButtonText, { color: theme.colors.error }]}>
                {t('driver.cancelRequest', 'Cancel Request')}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Tips */}
          <View style={styles.tipsContainer}>
            <Text style={[styles.tipsTitle, { color: theme.colors.textPrimary }]}>
              {t('whileYouWait', 'While you wait:')}
            </Text>
            <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
              • {t('tip1', 'Keep your phone nearby for updates')}
            </Text>
            <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
              • {t('tip2', 'Ensure your vehicle is safely parked')}
            </Text>
            <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
              • {t('tip3', 'Have your vehicle documents ready')}
            </Text>
          </View>
        </View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: width * 0.9,
    maxHeight: height * 0.8,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    overflow: 'hidden', // Prevent content from going outside
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    width: '100%',
    paddingHorizontal: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    flexWrap: 'wrap',
    maxWidth: '100%',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 22,
    flexWrap: 'wrap',
    maxWidth: '100%',
  },
  loadingContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  pulseContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  innerCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  spinner: {
    position: 'absolute',
    bottom: -10,
  },
  infoCard: {
    width: '100%',
    borderRadius: 12,
    padding: 10,
    marginBottom: 18,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  timerContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  timerLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  timerText: {
    fontSize: 32,
    fontWeight: 'bold',
    fontFamily: 'monospace',
  },
  statusContainer: {
    width: '100%',
    marginBottom: 24,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  statusText: {
    fontSize: 14,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 16,
  },
  cancelButton: {
    borderWidth: 2,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  tipsContainer: {
    width: '100%',
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    marginBottom: 4,
    lineHeight: 20,
  },
});

export default ServiceRequestWaitingUI;
