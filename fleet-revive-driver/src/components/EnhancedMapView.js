import React, { useState, useEffect, useRef } from "react";
import { StyleSheet, View, Alert, TouchableOpacity } from "react-native";
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "react-native-maps";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import CustomText from "./CustomText";
import locationService from "../services/locationService";
import directionsService from "../services/directionsService";
import { ROUTE_CONFIG } from "../config/constants";

const EnhancedMapView = ({
  userLocation,
  serviceLocations = [],
  mechanicLocation = null,
  activeServiceId = null,
  showTraffic = true,
  showNearbyServices = true,
  onMapPress,
  style,
}) => {
  const { theme } = useTheme();
  const mapRef = useRef(null);
  const [mapType, setMapType] = useState("standard");
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [routeSteps, setRouteSteps] = useState([]);
  const [nearbyMechanics, setNearbyMechanics] = useState([]);
  const [estimatedTime, setEstimatedTime] = useState(null);
  const [showUserLocation, setShowUserLocation] = useState(true);
  const [isLoadingRoute, setIsLoadingRoute] = useState(false);

  // Note: Mock data removed - nearby mechanics should come from real API data
  // TODO: Implement real API call to fetch nearby mechanics

  useEffect(() => {
    if (showNearbyServices) {
      // TODO: Replace with real API call to fetch nearby mechanics
      // For now, set empty array to remove mock data
      setNearbyMechanics([]);
    }
  }, [showNearbyServices, userLocation]);

  useEffect(() => {
    if (__DEV__) {
      console.log('🗺️ Driver App: Route calculation trigger:', {
        hasMechanicLocation: !!mechanicLocation,
        hasUserLocation: !!userLocation,
        mechanicLat: mechanicLocation?.latitude,
        mechanicLng: mechanicLocation?.longitude,
        userLat: userLocation?.coords?.latitude,
        userLng: userLocation?.coords?.longitude,
      });
    }

    // Calculate route if mechanic location is available
    if (mechanicLocation && userLocation && mechanicLocation.latitude && mechanicLocation.longitude) {
      console.log('🗺️ Driver App: Starting route calculation...');
      calculateRoute();
    } else {
      console.log('🗺️ Driver App: Missing location data for route calculation');
      setRouteCoordinates([]);
      setRouteSteps([]);
      setEstimatedTime(null);
    }
  }, [mechanicLocation, userLocation]);

  const calculateRoute = async () => {
    if (!mechanicLocation || !userLocation) {
      console.log('🗺️ Driver App: Cannot calculate route - missing location data');
      return;
    }

    console.log('🗺️ Driver App: Calculating route between driver and mechanic...');
    setIsLoadingRoute(true);
    try {
      const origin = {
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
      };
      const destination = {
        latitude: mechanicLocation.latitude,
        longitude: mechanicLocation.longitude,
      };

      // Get detailed route with turn-by-turn directions
      const routeData = await directionsService.getRoute(origin, destination, {
        ...ROUTE_CONFIG,
        avoidTolls: false, // Allow tolls for faster routes
        alternatives: false, // Get single best route
      });

      // Set route coordinates from Google Directions API
      setRouteCoordinates(routeData.coordinates);
      setRouteSteps(routeData.steps);

      // Set estimated time from API response
      const durationMinutes = Math.round(routeData.summary.duration.value / 60);
      setEstimatedTime(durationMinutes);

      console.log(`🚗 Driver route calculated: ${(routeData.summary.distance.value / 1000).toFixed(1)}km, ${durationMinutes}min`);
      console.log(`🛣️ Route has ${routeData.steps.length} turn-by-turn steps`);

      // Fit map to show both locations and route
      if (mapRef.current && routeData.coordinates.length > 0) {
        const allCoordinates = [
          { latitude: userLocation.coords.latitude, longitude: userLocation.coords.longitude },
          { latitude: mechanicLocation.latitude, longitude: mechanicLocation.longitude },
          ...routeData.coordinates
        ];
        mapRef.current.fitToCoordinates(allCoordinates, {
          edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
          animated: true,
        });
      }

    } catch (error) {
      console.error("Error calculating route:", error);

      // Fallback to simple route if API fails
      const startLat = userLocation.coords.latitude;
      const startLng = userLocation.coords.longitude;
      const endLat = mechanicLocation.latitude;
      const endLng = mechanicLocation.longitude;

      const route = [
        { latitude: startLat, longitude: startLng },
        {
          latitude: startLat + (endLat - startLat) * 0.3,
          longitude: startLng + (endLng - startLng) * 0.3
        },
        {
          latitude: startLat + (endLat - startLat) * 0.7,
          longitude: startLng + (endLng - startLng) * 0.7
        },
        { latitude: endLat, longitude: endLng },
      ];

      setRouteCoordinates(route);

      // Calculate estimated time using fallback method
      try {
        const travelInfo = await locationService.getEstimatedTravelTime(
          startLat, startLng, endLat, endLng
        );
        setEstimatedTime(travelInfo);
      } catch (fallbackError) {
        console.error("Fallback time calculation failed:", fallbackError);
        setEstimatedTime(15); // Default 15 minutes
      }

      console.log("🚗 Using fallback route calculation");
    } finally {
      setIsLoadingRoute(false);
    }
  };

  const centerOnUser = () => {
    if (userLocation && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: userLocation.coords.latitude,
        longitude: userLocation.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 1000);
    }
  };

  const centerOnMechanic = () => {
    if (mechanicLocation && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: mechanicLocation.latitude,
        longitude: mechanicLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 1000);
    }
  };

  const fitToRoute = () => {
    if (routeCoordinates.length > 0 && mapRef.current) {
      mapRef.current.fitToCoordinates(routeCoordinates, {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      });
    }
  };

  const toggleMapType = () => {
    const types = ["standard", "satellite", "hybrid"];
    const currentIndex = types.indexOf(mapType);
    const nextIndex = (currentIndex + 1) % types.length;
    setMapType(types[nextIndex]);
  };

  const renderMechanicMarker = (mechanic) => (
    <Marker
      key={mechanic.id}
      coordinate={{
        latitude: mechanic.latitude,
        longitude: mechanic.longitude,
      }}
      title={mechanic.name}
      description={`Rating: ${mechanic.rating} • ${mechanic.distance} • ${mechanic.services.join(", ")}`}
      pinColor={mechanic.available ? "#4CAF50" : "#F44336"}
    >
      <View style={[
        styles.mechanicMarker,
        { backgroundColor: mechanic.available ? "#4CAF50" : "#F44336" }
      ]}>
        <Ionicons 
          name="build" 
          size={20} 
          color="white" 
        />
      </View>
    </Marker>
  );

  const renderServiceLocationMarker = (service) => (
    <Marker
      key={service.id}
      coordinate={{
        latitude: service.latitude,
        longitude: service.longitude,
      }}
      title={`${service.serviceType} Service`}
      description={service.description}
      pinColor={service.id === activeServiceId ? "#FF9800" : "#2196F3"}
    >
      <View style={[
        styles.serviceMarker,
        { backgroundColor: service.id === activeServiceId ? "#FF9800" : "#2196F3" }
      ]}>
        <Ionicons 
          name={service.serviceType === "Tire" ? "car-sport" : 
                service.serviceType === "Engine" ? "build" : "car"} 
          size={20} 
          color="white" 
        />
      </View>
    </Marker>
  );

  if (!userLocation) {
    return (
      <View style={[styles.container, styles.centered]}>
        <CustomText>Loading map...</CustomText>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <MapView
        ref={mapRef}
        style={styles.map}
        mapType={mapType}
        initialRegion={{
          latitude: userLocation.coords.latitude,
          longitude: userLocation.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        showsUserLocation={showUserLocation}
        showsMyLocationButton={false}
        showsCompass={true}
        showsTraffic={showTraffic}
        showsBuildings={true}
        showsIndoors={true}
        showsPointsOfInterest={true}
        onPress={onMapPress}
        customMapStyle={theme.mapStyle}
      >
        {/* User location circle */}
        {userLocation && (
          <Circle
            center={{
              latitude: userLocation.coords.latitude,
              longitude: userLocation.coords.longitude,
            }}
            radius={100}
            fillColor="rgba(66, 165, 245, 0.2)"
            strokeColor="rgba(66, 165, 245, 0.8)"
            strokeWidth={2}
          />
        )}

        {/* Mechanic location marker */}
        {mechanicLocation && (
          <Marker
            coordinate={{
              latitude: mechanicLocation.latitude,
              longitude: mechanicLocation.longitude,
            }}
            title="Assigned Mechanic"
            description="Your mechanic's current location"
            pinColor="#FF5722"
          >
            <View style={[styles.mechanicMarker, { backgroundColor: "#FF5722" }]}>
              <Ionicons name="person" size={20} color="white" />
            </View>
          </Marker>
        )}

        {/* Route polyline */}
        {routeCoordinates.length > 0 && (
          <Polyline
            coordinates={routeCoordinates}
            strokeColor={theme.colors.primary}
            strokeWidth={4}
            lineDashPattern={[]}
          />
        )}

        {/* Nearby mechanics */}
        {showNearbyServices && nearbyMechanics.map(renderMechanicMarker)}

        {/* Service locations */}
        {serviceLocations.map(renderServiceLocationMarker)}
      </MapView>

      {/* Route information overlay */}
      {routeCoordinates.length > 0 && estimatedTime && (
        <View style={styles.routeInfo}>
          <View style={styles.routeInfoContent}>
            <Ionicons name="navigation" size={16} color={theme.colors.primary} />
            <Text style={[styles.routeInfoText, { color: theme.colors.textPrimary }]}>
              {typeof estimatedTime === 'object' ? estimatedTime.text : `${estimatedTime} min`}
            </Text>
            {typeof estimatedTime === 'object' && estimatedTime.distance && (
              <>
                <Text style={[styles.routeInfoSeparator, { color: theme.colors.textSecondary }]}>•</Text>
                <Text style={[styles.routeInfoText, { color: theme.colors.textSecondary }]}>
                  {estimatedTime.distance}
                </Text>
              </>
            )}
          </View>
          {isLoadingRoute && (
            <View style={styles.routeLoadingIndicator}>
              <Text style={[styles.routeLoadingText, { color: theme.colors.textSecondary }]}>
                Calculating route...
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Map controls */}
      <View style={styles.mapControls}>
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.surface }]}
          onPress={centerOnUser}
        >
          <Ionicons name="locate" size={24} color={theme.colors.primary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: theme.colors.surface }]}
          onPress={toggleMapType}
        >
          <Ionicons name="layers" size={24} color={theme.colors.primary} />
        </TouchableOpacity>

        {mechanicLocation && (
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.surface }]}
            onPress={centerOnMechanic}
          >
            <Ionicons name="person" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        )}

        {routeCoordinates.length > 0 && (
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: theme.colors.surface }]}
            onPress={fitToRoute}
          >
            <Ionicons name="resize" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Estimated time display */}
      {estimatedTime && (
        <View style={[styles.timeDisplay, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="time" size={16} color={theme.colors.primary} />
          <CustomText style={[styles.timeText, { color: theme.colors.textPrimary }]}>
            {estimatedTime.durationText} • {estimatedTime.distanceText}
          </CustomText>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: "absolute",
    right: 16,
    top: 60,
    gap: 8,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  routeInfo: {
    position: "absolute",
    top: 60,
    left: 16,
    right: 80,
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 12,
    padding: 12,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  routeInfoContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  routeInfoText: {
    fontSize: 14,
    fontWeight: "600",
  },
  routeInfoSeparator: {
    fontSize: 14,
    fontWeight: "400",
  },
  routeLoadingIndicator: {
    marginTop: 4,
  },
  routeLoadingText: {
    fontSize: 12,
    fontStyle: "italic",
  },
  mechanicMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
  },
  serviceMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
  },
  timeDisplay: {
    position: "absolute",
    top: 16,
    left: 16,
    right: 16,
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  timeText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: "500",
  },
});

export default EnhancedMapView;
