{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "done": "Done", "retry": "Retry", "close": "Close", "ok": "OK", "yes": "Yes", "no": "No", "with": "with"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "signup": "Sign Up", "register": "Register", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Full Name", "phone": "Phone Number", "loginButton": "Sign In", "signupButton": "Create Account", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "loginSuccess": "Login successful!", "signupSuccess": "Account created successfully!", "loginError": "<PERSON><PERSON> failed. Please check your credentials.", "signupError": "Registration failed. Please try again.", "invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match", "fieldRequired": "This field is required", "phoneOptional": "Phone Number (Optional)", "with": "with"}, "navigation": {"home": "Home", "profile": "Profile", "settings": "Settings", "history": "History", "notifications": "Notifications", "help": "Help", "about": "About"}, "driver": {"dashboard": "Driver Dashboard", "requestService": "Request Service", "serviceHistory": "Service History", "currentLocation": "Current Location", "nearbyMechanics": "Nearby Mechanics", "requestSent": "Request sent to nearby mechanics", "searchingMechanics": "Searching for available mechanics...", "signInWithGoogle": "Sign in with Google", "serviceTypes": {"tire": "Tire Service", "battery": "Battery Service", "engine": "Engine Service", "brake": "Brake Service", "oil": "Oil Change", "towing": "Towing Service", "other": "Other Service"}, "serviceStatus": {"pending": "Pending", "assigned": "Assigned", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}, "requestServiceTitle": "Request Roadside Assistance", "selectServiceType": "Select Service Type", "describeIssue": "Describe the Issue", "submitRequest": "Submit Request", "requestSubmitted": "Service request submitted successfully!", "waitingForMechanic": "Waiting for a mechanic to accept your request...", "mechanicAssigned": "Mechanic assigned to your request", "mechanicOnWay": "Mechanic is on the way", "serviceCompleted": "Service completed successfully!", "rateService": "Rate this service", "addReview": "Add a review (optional)"}, "settings": {"title": "Settings", "darkMode": "Dark Mode", "language": "Language", "notifications": "Notifications", "privacy": "Privacy", "terms": "Terms of Service", "support": "Support", "version": "Version", "languageSelection": "Select Language", "english": "English", "french": "Français", "notificationSettings": "Notification Settings", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "serviceUpdates": "Service Updates", "promotions": "Promotions", "languageChanged": "Language changed successfully"}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "vehicleInfo": "Vehicle Information", "rating": "Rating", "totalServices": "Total Services", "memberSince": "Member Since", "updateProfile": "Update Profile", "profileUpdated": "Profile updated successfully", "uploadPhoto": "Upload Photo", "changePhoto": "Change Photo", "removePhoto": "Remove Photo"}, "notifications": {"title": "Notifications", "noNotifications": "No notifications", "markAllRead": "<PERSON> as <PERSON>", "newServiceRequest": "New Service Request", "mechanicAssigned": "Mechanic Assigned", "serviceUpdate": "Service Update", "serviceCompleted": "Service Completed", "paymentReceived": "Payment Received", "ratingReceived": "Rating Received"}, "errors": {"networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "locationError": "Unable to get your location. Please enable location services.", "permissionDenied": "Permission denied. Please grant the required permissions.", "invalidInput": "Invalid input. Please check your data.", "sessionExpired": "Session expired. Please login again.", "serviceUnavailable": "Service temporarily unavailable. Please try again later.", "invitationRequired": "Invitation Required", "invitationRequiredMessage": "You need to be invited by a repair company admin to register as a mechanic. Please contact your company administrator to send you an invitation.", "invitationExpired": "Invitation Expired", "invitationExpiredMessage": "Your invitation has expired. Please contact your company administrator to send you a new invitation.", "invitationUsed": "Invitation Already Used", "invitationUsedMessage": "This invitation has already been used. If you already have an account, please try logging in instead.", "emailExists": "Email Already Registered", "emailExistsMessage": "This email is already registered. Please try logging in or use a different email address.", "genericSignupError": "An error occurred during signup. Please try again."}, "welcome": {"title": "Welcome to FleetRevive", "subtitle": "Your trusted roadside assistance partner", "getStarted": "Get Started", "learnMore": "Learn More", "features": {"quickService": "Quick Service", "quickServiceDesc": "Get help within minutes", "trustedMechanics": "Trusted Mechanics", "trustedMechanicsDesc": "Verified and experienced professionals", "realTimeTracking": "Real-time Tracking", "realTimeTrackingDesc": "Track your mechanic's location"}}, "mechanic": {"dashboard": "Mechanic Dashboard", "availableJobs": "Available Jobs", "activeJobs": "Active Jobs", "jobHistory": "Job History", "currentLocation": "Current Location", "nearbyRequests": "Nearby Requests", "welcomeMessage": "Welcome to FleetRevive! You have successfully registered as a mechanic.", "acceptJob": "Accept Job", "declineJob": "Decline Job", "startService": "Start Service", "completeService": "Complete Service", "jobAccepted": "Job accepted successfully!", "onMyWay": "I'm on my way", "arrivedAtLocation": "Arrived at location", "serviceInProgress": "Service in progress", "jobCompleted": "Job completed successfully!", "rateCustomer": "Rate this customer", "addNotes": "Add service notes (optional)", "estimatedArrival": "Estimated arrival time", "contactCustomer": "Contact Customer", "viewJobDetails": "View Job Details", "updateLocation": "Update Location"}, "historyScreen": {"title": "Service History", "filterAll": "All", "filterPending": "Pending", "filterCompleted": "Completed", "filterCancelled": "Cancelled", "noHistory": "No service history found", "noFilteredHistory": "No {status} services found"}}