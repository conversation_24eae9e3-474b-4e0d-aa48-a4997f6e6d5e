{"common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "cancel": "Annuler", "confirm": "Confirmer", "save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "back": "Retour", "next": "Suivant", "done": "<PERSON><PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "O<PERSON>", "no": "Non", "with": "avec"}, "auth": {"login": "Connexion", "logout": "Déconnexion", "signup": "S'inscrire", "register": "S'enregistrer", "forgotPassword": "Mot de passe oublié ?", "resetPassword": "Réinitialiser le mot de passe", "email": "Email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "name": "Nom complet", "phone": "Numéro de téléphone", "loginButton": "Se connecter", "signupButton": "<PERSON><PERSON><PERSON> un compte", "alreadyHaveAccount": "Vous avez déjà un compte ?", "dontHaveAccount": "Vous n'avez pas de compte ?", "loginSuccess": "Connexion réussie !", "signupSuccess": "Compte créé avec succès !", "loginError": "Échec de la connexion. Vérifiez vos identifiants.", "signupError": "Échec de l'inscription. Veuillez réessayer.", "invalidEmail": "Veu<PERSON>z entrer une adresse email valide", "passwordTooShort": "Le mot de passe doit contenir au moins 6 caractères", "passwordMismatch": "Les mots de passe ne correspondent pas", "fieldRequired": "Ce champ est obligatoire", "phoneOptional": "Numéro de téléphone (Optionnel)", "with": "avec"}, "navigation": {"home": "Accueil", "profile": "Profil", "settings": "Paramètres", "history": "Historique", "notifications": "Notifications", "help": "Aide", "about": "À propos"}, "driver": {"dashboard": "Tableau de bord conducteur", "requestService": "Demander un service", "serviceHistory": "Historique des services", "currentLocation": "Position actuelle", "nearbyMechanics": "Mécaniciens à proximité", "requestSent": "De<PERSON><PERSON> envoyée aux mécaniciens à proximité", "searchingMechanics": "Recherche de mécaniciens disponibles...", "signInWithGoogle": "Se connecter avec Google", "serviceTypes": {"tire": "Service de pneus", "battery": "Service de batterie", "engine": "Service moteur", "brake": "Service de freins", "oil": "Vidange d'huile", "towing": "Service de remorquage", "other": "Autre service"}, "serviceStatus": {"pending": "En attente", "assigned": "<PERSON><PERSON><PERSON>", "inProgress": "En cours", "completed": "<PERSON><PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}, "requestServiceTitle": "Demander une assistance routière", "selectServiceType": "Sélectionner le type de service", "describeIssue": "Dé<PERSON><PERSON><PERSON> le problème", "submitRequest": "Soumettre la demande", "requestSubmitted": "Demande de service soumise avec succès !", "waitingForMechanic": "En attente qu'un mécanicien accepte votre demande...", "mechanicAssigned": "Mécanicien assigné à votre demande", "mechanicOnWay": "Le mécanicien est en route", "serviceCompleted": "Service terminé avec succès !", "rateService": "Évaluer ce service", "addReview": "Ajouter un avis (optionnel)"}, "settings": {"title": "Paramètres", "darkMode": "Mode sombre", "language": "<PERSON><PERSON>", "notifications": "Notifications", "privacy": "Confidentialité", "terms": "Conditions d'utilisation", "support": "Support", "version": "Version", "languageSelection": "Sélectionner la langue", "english": "English", "french": "Français", "notificationSettings": "Paramètres de notification", "pushNotifications": "Notifications push", "emailNotifications": "Notifications par email", "smsNotifications": "Notifications SMS", "serviceUpdates": "Mises à jour de service", "promotions": "Promotions", "languageChanged": "Langue modifiée avec succès"}, "profile": {"title": "Profil", "editProfile": "Modifier le profil", "personalInfo": "Informations personnelles", "contactInfo": "Informations de contact", "vehicleInfo": "Informations du véhicule", "rating": "Évaluation", "totalServices": "Total des services", "memberSince": "Membre depuis", "updateProfile": "Mettre à jour le profil", "profileUpdated": "Profil mis à jour avec succès", "uploadPhoto": "Télécharger une photo", "changePhoto": "Changer la photo", "removePhoto": "Supprimer la photo"}, "notifications": {"title": "Notifications", "noNotifications": "Aucune notification", "markAllRead": "Tout marquer comme lu", "newServiceRequest": "Nouvelle demande de service", "mechanicAssigned": "Mécanicien <PERSON>", "serviceUpdate": "Mise à jour du service", "serviceCompleted": "Service terminé", "paymentReceived": "Pa<PERSON><PERSON> reçu", "ratingReceived": "Évaluation reçue"}, "errors": {"networkError": "<PERSON><PERSON><PERSON> réseau. Vérifiez votre connexion.", "serverError": "Erreur serveur. Veuillez réessayer plus tard.", "locationError": "Impossible d'obtenir votre position. Activez les services de localisation.", "permissionDenied": "Permission refusée. Accordez les permissions requises.", "invalidInput": "Entrée invalide. Vérifiez vos données.", "sessionExpired": "Session expirée. Veuillez vous reconnecter.", "serviceUnavailable": "Service temporairement indisponible. Veuillez réessayer plus tard.", "invitationRequired": "Invitation requise", "invitationRequiredMessage": "Vous devez être invité par un administrateur d'entreprise de réparation pour vous inscrire en tant que mécanicien. Veuillez contacter votre administrateur d'entreprise pour qu'il vous envoie une invitation.", "invitationExpired": "Invitation expirée", "invitationExpiredMessage": "Votre invitation a expiré. Veuillez contacter votre administrateur d'entreprise pour qu'il vous envoie une nouvelle invitation.", "invitationUsed": "Invitation déjà utilisée", "invitationUsedMessage": "Cette invitation a déjà été utilisée. Si vous avez déjà un compte, veuillez essayer de vous connecter à la place.", "emailExists": "<PERSON>ail d<PERSON> enregistré", "emailExistsMessage": "Cet email est déjà enregistré. Veuillez essayer de vous connecter ou utiliser une adresse email différente.", "genericSignupError": "Une erreur s'est produite lors de l'inscription. Veuillez réessayer."}, "welcome": {"title": "Bienvenue sur FleetRevive", "subtitle": "Votre partenaire de confiance pour l'assistance routière", "getStarted": "Commencer", "learnMore": "En savoir plus", "features": {"quickService": "Service rapide", "quickServiceDesc": "Obt<PERSON>z de l'aide en quelques minutes", "trustedMechanics": "Mécaniciens de confiance", "trustedMechanicsDesc": "Professionnels vérifiés et expérimentés", "realTimeTracking": "Suivi en temps réel", "realTimeTrackingDesc": "<PERSON><PERSON><PERSON> la position de votre mécanicien"}}, "mechanic": {"dashboard": "Tableau de bord mécanicien", "availableJobs": "<PERSON><PERSON><PERSON><PERSON> disponibles", "activeJobs": "Emplois actifs", "jobHistory": "Historique des emplois", "currentLocation": "Position actuelle", "nearbyRequests": "Demandes à proximité", "welcomeMessage": "Bienvenue sur FleetRevive ! Vous vous êtes inscrit avec succès en tant que mécanicien.", "acceptJob": "Accepter l'emploi", "declineJob": "Refuser l'emploi", "startService": "Commencer le service", "completeService": "Terminer le service", "jobAccepted": "Emploi accepté avec succès !", "onMyWay": "Je suis en route", "arrivedAtLocation": "Arrivé à l'emplacement", "serviceInProgress": "Service en cours", "jobCompleted": "Emploi terminé avec succès !", "rateCustomer": "Évaluer ce client", "addNotes": "Ajouter des notes de service (optionnel)", "estimatedArrival": "Heure d'arrivée estimée", "contactCustomer": "Contacter le client", "viewJobDetails": "Voir les détails de l'emploi", "updateLocation": "Mettre à jour la position"}, "historyScreen": {"title": "Historique des services", "filterAll": "Tous", "filterPending": "En attente", "filterCompleted": "<PERSON><PERSON><PERSON><PERSON>", "filterCancelled": "<PERSON><PERSON><PERSON>", "noHistory": "Aucun historique de service trouvé", "noFilteredHistory": "Aucun service {status} trouvé"}}